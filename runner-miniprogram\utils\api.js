const BASE_URL = 'http://localhost:5000/api';

const request = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${BASE_URL}${url}`,
      ...options,
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(new Error(err.errMsg));
      }
    });
  });
};

const api = {
  // 任务相关接口
  getTasks: () => {
    return request('/tasks', {
      method: 'GET'
    });
  },

  addTask: (task) => {
    return request('/tasks', {
      method: 'POST',
      data: { 任务: task }
    });
  },

  deleteTask: (taskId) => {
    return request(`/tasks/${taskId}`, {
      method: 'DELETE'
    });
  }
};

module.exports = api;
