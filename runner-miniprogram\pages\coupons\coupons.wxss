page {
    background-color: #f5f5f5;
}

.coupons-container {
    padding: 20rpx;
}

.coupons-tabs {
    display: flex;
    justify-content: space-around;
    background-color: white;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
}

.coupons-tab {
    flex: 1;
    text-align: center;
    padding: 20rpx;
    color: #666;
}

.coupons-tab.active {
    color: #07c160;
    border-bottom: 4rpx solid #07c160;
}

.coupons-list {
    height: calc(100vh - 100rpx);
}

.coupon-item {
    display: flex;
    background-color: white;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
}

.coupon-item.used {
    opacity: 0.5;
}

.coupon-item.expired {
    opacity: 0.3;
}

.coupon-left {
    background-color: #07c160;
    color: white;
    width: 150rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20rpx;
}

.coupon-amount {
    font-size: 48rpx;
    font-weight: bold;
}

.coupon-condition {
    font-size: 24rpx;
    margin-top: 10rpx;
}

.coupon-right {
    flex-grow: 1;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.coupon-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
}

.coupon-time {
    font-size: 28rpx;
    color: #666;
}
