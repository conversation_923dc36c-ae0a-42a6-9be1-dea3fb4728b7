page {
    background-color: #f5f7f9;  /* 更柔和的背景 */
}

.wallet-container {
    padding: 30rpx;
}

.wallet-header {
    background: linear-gradient(135deg, #07c160, #05a050);  /* 渐变背景 */
    color: white;
    text-align: center;
    padding: 50rpx 0;
    border-radius: 20rpx;
    box-shadow: 0 10rpx 20rpx rgba(7, 193, 96, 0.2);  /* 轻微阴影 */
}

.wallet-balance-title {
    display: block;
    font-size: 28rpx;
    margin-bottom: 15rpx;
    opacity: 0.8;
}

.wallet-balance {
    font-size: 64rpx;
    font-weight: bold;
    letter-spacing: 2rpx;
}

.wallet-actions {
    display: flex;
    justify-content: space-around;
    background-color: white;
    border-radius: 20rpx;
    margin: 30rpx 0;
    padding: 30rpx;
    box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.05);
}

.wallet-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.2s;
}

.wallet-action:active {
    transform: scale(0.95);
}

.wallet-action-icon {
    font-size: 52rpx;
    margin-bottom: 15rpx;
    color: #07c160;
}

.wallet-action-text {
    font-size: 28rpx;
    color: #333;
}

.wallet-bill-header {
    background-color: white;
    padding: 20rpx 30rpx;
    font-size: 34rpx;
    font-weight: bold;
    border-radius: 20rpx 20rpx 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.05);
}

.wallet-bill-list {
    max-height: 600rpx;
    overflow-y: auto;
}

.bill-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    transition: background-color 0.2s;
}

.bill-item:hover {
    background-color: #f9f9f9;
}

.bill-item:last-child {
    border-bottom: none;
}

.bill-info {
    display: flex;
    align-items: center;
}

.bill-icon {
    font-size: 48rpx;
    margin-right: 20rpx;
    color: #07c160;
}

.bill-title {
    font-size: 32rpx;
    color: #333;
}

.bill-time {
    font-size: 24rpx;
    color: #999;
    margin-top: 5rpx;
}

.bill-amount {
    font-size: 34rpx;
    font-weight: 500;
}

.bill-amount.income {
    color: #07c160;
}

.bill-amount.expense {
    color: #ff4d4f;
}
