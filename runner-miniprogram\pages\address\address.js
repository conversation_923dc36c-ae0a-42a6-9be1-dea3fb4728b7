Page({
    data: {
        addresses: [
            {
                name: '张三',
                phone: '13800138000',
                detail: '北京市顺义区后沙峪镇',
                isDefault: true
            },
            {
                name: '李四',
                phone: '15912345678',
                detail: '北京市朝阳区望京soho',
                isDefault: false
            }
        ]
    },

    onLoad: function() {
        // 可以在这里加载真实的地址数据
    },

    selectAddress: function(e) {
        const index = e.currentTarget.dataset.index;
        const selectedAddress = this.data.addresses[index];
        
        // 如果是从下单页面选择地址
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2];
        if (prevPage && prevPage.route === 'pages/order/order') {
            prevPage.setData({
                selectedAddress: selectedAddress
            });
            wx.navigateBack();
        }
    },

    addNewAddress: function() {
        wx.navigateTo({
            url: '/pages/address-edit/address-edit'
        });
    },

    editAddress: function(e) {
        const index = e.currentTarget.dataset.index;
        wx.navigateTo({
            url: `/pages/address-edit/address-edit?index=${index}`
        });
    },

    deleteAddress: function(e) {
        const index = e.currentTarget.dataset.index;
        wx.showModal({
            title: '删除地址',
            content: '确定要删除这个地址吗？',
            success: (res) => {
                if (res.confirm) {
                    const addresses = this.data.addresses;
                    addresses.splice(index, 1);
                    this.setData({
                        addresses: addresses
                    });
                }
            }
        });
    }
});
