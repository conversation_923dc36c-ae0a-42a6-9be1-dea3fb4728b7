Page({
    data: {
        hasUserInfo: false,
        loginLoading: false,
        privacyAgreed: false,
        animationTimer: null
    },

    onLoad: function() {
        // 检查本地是否已有用户信息
        const userInfo = wx.getStorageSync('userInfo');
        if (userInfo) {
            this.navigateToMainPage();
        }
        
        this.initStarryBackground();
    },

    initStarryBackground() {
        const query = wx.createSelectorQuery();
        query.select('#starryCanvas')
            .fields({ node: true, size: true })
            .exec((res) => {
                const canvas = res[0].node;
                const ctx = canvas.getContext('2d');
                const dpr = wx.getSystemInfoSync().pixelRatio;
                canvas.width = res[0].width * dpr;
                canvas.height = res[0].height * dpr;
                ctx.scale(dpr, dpr);

                const stars = [];
                const starCount = 100;

                for (let i = 0; i < starCount; i++) {
                    stars.push({
                        x: Math.random() * canvas.width,
                        y: Math.random() * canvas.height,
                        radius: Math.random() * 1.5,
                        speed: Math.random() * 0.3 + 0.1,
                        opacity: Math.random() * 0.8 + 0.2
                    });
                }

                const animate = () => {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    stars.forEach(star => {
                        ctx.beginPath();
                        ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
                        ctx.fillStyle = `rgba(255, 255, 255, ${star.opacity})`;
                        ctx.fill();

                        star.x += star.speed;
                        star.y += star.speed * 0.5;

                        if (star.x > canvas.width) star.x = 0;
                        if (star.y > canvas.height) star.y = 0;
                    });

                    this.data.animationTimer = canvas.requestAnimationFrame(animate);
                };

                animate();
            });
    },

    togglePrivacyAgreement() {
        this.setData({
            privacyAgreed: !this.data.privacyAgreed
        });
    },

    showPrivacyPolicy() {
        wx.navigateTo({
            url: '/pages/privacy/privacy'
        });
    },

    navigateToMainPage() {
        wx.redirectTo({
            url: '/pages/index/index'
        });
    },

    handleWechatLogin() {
        // 检查是否同意隐私协议
        if (!this.data.privacyAgreed) {
            wx.showToast({
                title: '请先同意隐私协议',
                icon: 'none'
            });
            return;
        }

        // 设置登录状态
        this.setData({ loginLoading: true });

        // 微信登录
        wx.login({
            success: (res) => {
                if (res.code) {
                    // 发送 code 到后端换取 openid 和 session_key
                    wx.request({
                        url: 'https://your-api.com/login',
                        method: 'POST',
                        data: { code: res.code },
                        success: (loginRes) => {
                            // 保存用户信息
                            wx.setStorageSync('userInfo', loginRes.data.userInfo);
                            
                            this.setData({
                                hasUserInfo: true,
                                loginLoading: false
                            });

                            wx.showToast({
                                title: '登录成功',
                                icon: 'success'
                            });

                            this.navigateToMainPage();
                        },
                        fail: () => {
                            this.setData({ loginLoading: false });
                            wx.showToast({
                                title: '登录失败',
                                icon: 'none'
                            });
                        }
                    });
                }
            },
            fail: () => {
                this.setData({ loginLoading: false });
                wx.showToast({
                    title: '微信登录失败',
                    icon: 'none'
                });
            }
        });
    },

    handleDebugLogin() {
        // 调试登录，直接设置测试用户信息
        wx.setStorageSync('userInfo', {
            nickName: '测试用户',
            avatarUrl: '/images/default-avatar.png'
        });

        this.setData({
            hasUserInfo: true
        });

        wx.showToast({
            title: '调试登录成功',
            icon: 'success'
        });

        this.navigateToMainPage();
    },

    onUnload: function() {
        if (this.data.animationTimer) {
            cancelAnimationFrame(this.data.animationTimer);
        }
    }
});
