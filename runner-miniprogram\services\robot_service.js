const request = require('../utils/request.js');

class RobotService {
    constructor() {
        this.baseUrl = 'https://your-backend-url.com/api';
    }

    // 语音输入
    startVoiceInput() {
        return new Promise((resolve, reject) => {
            wx.startRecord({
                success: (res) => {
                    wx.uploadFile({
                        url: `${this.baseUrl}/transcribe`,
                        filePath: res.tempFilePath,
                        name: 'audio',
                        success: (uploadRes) => {
                            resolve(uploadRes.data.text);
                        },
                        fail: reject
                    });
                },
                fail: reject
            });
        });
    }

    // 意图分析
    analyzeIntent(text) {
        return request.post(`${this.baseUrl}/analyze_intent`, { text });
    }

    // 处理查询
    processQuery(query) {
        return request.post(`${this.baseUrl}/query`, { query });
    }
}

module.exports = new RobotService();
