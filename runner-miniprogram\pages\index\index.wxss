/* 引入图标字体 */
@import '../../iconfont/iconfont.wxss';

/* 全局容器 */
.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f5f5f5;
}

/* 聊天消息区域样式 */
.chat-messages {
    flex-grow: 1;
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    background-color: #F5F5F5;
}

/* 消息容器 */
.message-item {
    display: flex;
    margin-bottom: 20rpx;
    width: 100%;
    align-items: flex-end;
}

/* 消息包装器 */
.message-wrapper {
    display: flex;
    max-width: 70%;
    align-items: flex-end;
}

/* 用户消息容器 */
.user-message-container {
    justify-content: flex-end;
}

.user-message-wrapper {
    flex-direction: row-reverse;
    align-self: flex-end;
    align-items: flex-end;
}

.user-message-content {
    background-color: #E6F3FF;
    color: #333333;
    padding: 15rpx 20rpx;
    border-radius: 16rpx;
    margin-right: 15rpx;
    max-width: 100%;
    word-break: break-word;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

/* AI消息容器 */
.ai-message-container {
    justify-content: flex-start;
}

.ai-message-wrapper {
    flex-direction: row;
    align-items: flex-end;
}

.ai-message-content {
    background-color: #FFFFFF;
    color: #333333;
    padding: 15rpx 20rpx;
    border-radius: 16rpx;
    margin-left: 15rpx;
    border: 1rpx solid #E5E5E5;
    max-width: 100%;
    word-break: break-word;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

/* 系统消息 */
.system-message {
    width: 100%;
    text-align: center;
    color: #888888;
    font-size: 24rpx;
    margin: 20rpx 0;
    background-color: #F0F0F0;
    padding: 10rpx;
    border-radius: 8rpx;
}

/* 头像 */
.avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    object-fit: cover;  /* 保持图片比例 */
    background-color: #E0E0E0;  /* 默认背景 */
    border: 2rpx solid #FFFFFF;  /* 白色边框 */
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.user-avatar {
    margin-left: 15rpx;
}

.ai-avatar {
    margin-right: 15rpx;
}

/* 置信度标签 */
.confidence-tag {
    font-size: 20rpx;
    color: #888888;
    margin-top: 8rpx;
    text-align: right;
}

/* 错误状态 */
.message-status.error {
    color: #FF4444;
    font-size: 20rpx;
    margin-top: 8rpx;
}

/* 输入区域 */
.input-container {
    background-color: #f5f5f5;
    padding: 20rpx;
    border-top: 1rpx solid #e5e5e5;
}

.input-actions {
    display: flex;
    align-items: center;
}

.chat-input {
    flex-grow: 1;
    background-color: #ffffff;
    border-radius: 10rpx;
    padding: 15rpx;
    margin: 0 20rpx;
    font-size: 32rpx;
}

.action-btn {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
    padding: 0;
}

/* 加载指示器 */
.loading-indicator {
    text-align: center;
    padding: 20rpx;
    background-color: #f0f0f0;
}

.loading-text {
    color: #888;
    font-size: 28rpx;
}

/* 推荐主题 */
.recommendation-panel {
    background-color: #f0f0f0;
    padding: 15rpx;
    text-align: center;
}

.recommendation-panel text {
    color: #666;
    font-size: 28rpx;
}

/* 图标字体 */
.iconfont {
    font-size: 40rpx;
    color: #666;
}

/* 图标样式 */
.icon {
    width: 40rpx;
    height: 40rpx;
}

.action-btn .icon {
    width: 50rpx;
    height: 50rpx;
}

/* 登录页面样式 */
.page {
    background-color: #f5f5f5;
    height: 100vh;
}

.page-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

/* 聊天页面样式 */
.chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    background-color: rgba(255, 255, 255, 0.2);  
    backdrop-filter: blur(20rpx);
    -webkit-backdrop-filter: blur(20rpx);
    box-shadow: 0 4rpx 6rpx rgba(102, 204, 170, 0.05);
}

.header-left {
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.header-icon {
    width: 50rpx;
    height: 50rpx;
    opacity: 0.8;
    margin-left: 20rpx;
}

.header-more {
    width: 50rpx;
    height: 50rpx;
    opacity: 0.7;
}

.header-right {
    display: flex;
    justify-content: flex-end;  
    align-items: center;
    margin-right: 20rpx;  
}

.header-title {
    width: 100rpx; 
    text-align: left;
    display: flex;
    align-items: center;
    padding-left: 20rpx;
    font-weight: bold;
    color: white;
    font-size: 36rpx;
}

.notice-marquee {
    width: 100%;
    display: flex;
    align-items: center;
    background-color: transparent;
}

.notice-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 10rpx;
}

.notice-swiper {
    flex-grow: 1;
    height: 50rpx;
}

.notice-item {
    display: flex;
    align-items: center;
}

.notice-text {
    font-size: 28rpx;
    color: rgba(20, 80, 60, 0.8);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-content {
    flex-grow: 1;
    overflow-y: scroll;
    padding: 20rpx;
    transition: height 0.3s ease;
}

.chat-content.input-focused {
    height: calc(100vh - 300rpx); 
}

.input-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(240, 255, 250, 0.08);
    backdrop-filter: blur(15rpx);
    -webkit-backdrop-filter: blur(15rpx);
    box-shadow: 0 -4rpx 6rpx rgba(102, 204, 170, 0.03);
    padding: 10rpx;
    z-index: 100;
}

.input-container.keyboard-active {
    transform: translateY(-{{keyboardHeight}}px);
}

.input-tools {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.input-icon {
    width: 50rpx;
    height: 50rpx;
    margin: 0 10rpx;
}

.input-area {
    flex-grow: 1;
    margin: 0 10rpx;
}

.text-input-container {
    display: flex;
    align-items: center;
}

.chat-text-input {
    flex-grow: 1;
    background-color: #f0f0f0;
    border-radius: 20rpx;
    padding: 10rpx 15rpx;
    font-size: 28rpx;
    margin-right: 10rpx;
}

.send-btn {
    background-color: #07c160;
    color: white;
    padding: 10rpx 20rpx;
    border-radius: 20rpx;
    font-size: 28rpx;
}

.chat-voice-input-btn {
    background-color: #f0f0f0;
    border-radius: 20rpx;
    padding: 15rpx;
    text-align: center;
}

.chat-voice-input-btn.recording {
    background-color: #ff4d4f;
    color: white;
}

.more-actions {
    width: 45rpx;
    height: 45rpx;
    margin-left: 10rpx;
    opacity: 0.7;
}

.more-actions-panel {
    position: absolute;
    bottom: 120rpx;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10rpx);
    border-radius: 30rpx;
    padding: 20rpx;
    margin: 0 20rpx;
    box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.more-actions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;
    justify-content: center;
    align-items: center;
}

.more-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15rpx;
    border-radius: 20rpx;
    transition: background-color 0.3s ease;
}

.more-action-item:active {
    background-color: rgba(102, 204, 170, 0.1);
}

.more-action-icon {
    width: 100rpx;
    height: 100rpx;
    background-color: rgba(102, 204, 170, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10rpx;
}

.icon-image {
    width: 60rpx;
    height: 60rpx;
    opacity: 0.8;
}

.action-text {
    font-size: 24rpx;
    color: rgba(0, 0, 0, 0.7);
    text-align: center;
}

.login-container {
    display: none;
}

.wx-tabbar {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
}
