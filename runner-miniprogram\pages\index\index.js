const API_CONFIG = {
    local: 'http://localhost:8000',
    development: 'http://127.0.0.1:8000',
    localIP: 'http://*************:8000',  // 使用本机 IP 地址
    production: 'https://your-production-domain.com',
    fallback: 'https://api.example.com'  // 添加备用 API
};

// 获取当前可用的 API 地址
function getAvailableAPIUrl() {
    const networkType = wx.getSystemInfoSync().platform;
    
    // 根据不同平台和网络环境选择 API
    switch(networkType) {
        case 'devtools':  // 开发者工具
            return API_CONFIG.local;
        case 'windows':   // Windows 环境
            return API_CONFIG.localIP;
        default:
            return API_CONFIG.production;
    }
}

const CURRENT_ENV = 'localIP'; // 切换到本地 IP
const API_BASE_URL = getAvailableAPIUrl();

const securityService = require('../../services/security_service.js');
const robotService = require('../../services/robot_service.js');

// 安全模块
const SecurityModule = {
    // 安全模块初始化
    initializeSecurity(context) {
        return new Promise((resolve, reject) => {
            try {
                // 获取系统信息
                const systemInfo = wx.getSystemInfoSync();

                // 异步获取网络类型
                wx.getNetworkType({
                    success: (res) => {
                        const networkType = res.networkType;
                        
                        // 风险评估
                        const riskLevel = this.assessRiskLevel(systemInfo, networkType);
                        
                        // 异常分数计算
                        const anomalyScore = this.calculateAnomalyScore(systemInfo);
                        
                        // 安全监控
                        this.startSecurityMonitoring(context);
                        
                        // 记录安全初始化信息
                        wx.setStorageSync('securityInitInfo', {
                            timestamp: Date.now(),
                            systemInfo: systemInfo,
                            networkType: networkType,
                            riskLevel: riskLevel,
                            anomalyScore: anomalyScore
                        });
                        
                        resolve({
                            systemInfo,
                            networkType,
                            riskLevel,
                            anomalyScore
                        });
                    },
                    fail: (error) => {
                        console.error('获取网络类型失败', error);
                        // 即使网络获取失败，也继续进行其他安全初始化
                        const riskLevel = this.assessRiskLevel(systemInfo, 'unknown');
                        const anomalyScore = this.calculateAnomalyScore(systemInfo);
                        
                        this.startSecurityMonitoring(context);
                        
                        resolve({
                            systemInfo,
                            networkType: 'unknown',
                            riskLevel,
                            anomalyScore
                        });
                    }
                });
            } catch (error) {
                console.error('安全模块初始化失败', error);
                reject(error);
            }
        });
    },

    // 风险评估
    assessRiskLevel(systemInfo, networkType) {
        let riskScore = 0;

        // 系统版本风险
        const osVersion = systemInfo.system;
        if (osVersion.includes('iOS') && parseInt(osVersion.split(' ')[1]) < 14) {
            riskScore += 20;
        }

        // 网络类型风险
        if (networkType === 'none') {
            riskScore += 30;
        } else if (networkType === 'wifi') {
            riskScore -= 10;
        }

        // 设备风险
        if (systemInfo.platform === 'android') {
            riskScore += 10;
        }

        return riskScore > 30 ? 'high' : 
               riskScore > 15 ? 'medium' : 'low';
    },

    // 异常分数计算
    calculateAnomalyScore(systemInfo) {
        const baseScore = 0;
        
        // 根据系统参数计算风险
        const memoryRisk = systemInfo.memory > 4096 ? 0 : 10;
        const storageRisk = systemInfo.storage < 1024 ? 10 : 0;
        
        return baseScore + memoryRisk + storageRisk;
    },

    // 安全监控
    startSecurityMonitoring(context) {
        // 定期检查网络和系统状态
        const monitorInterval = setInterval(() => {
            wx.getNetworkType({
                success: (res) => {
                    if (res.networkType === 'none') {
                        wx.showToast({
                            title: '网络已断开',
                            icon: 'none'
                        });
                    }
                }
            });
        }, 60000); // 每分钟检查一次

        // 存储定时器，以便后续可以清除
        context.securityMonitorInterval = monitorInterval;
    }
};

// 情感分析映射表
const sentimentMap = {
    positive: ['好', '棒', '赞', 'excellent', 'great'],
    negative: ['不', '差', 'bad', 'terrible', 'horrible'],
    neutral: ['一般', 'okay', 'fine']
};

Page({
    data: {
        notices: [
            '欢迎使用小六助手！今日天气不错',
            '新功能：语音输入已上线，快来体验吧',
            '系统将在凌晨进行维护，预计10分钟'
        ],
        messages: [
            { 
                type: 'ai', 
                content: '你好，我是小六助手！很高兴为你服务。' 
            }
        ],
        inputMessage: '',
        inputPlaceholder: '输入您的消息',
        inputMode: 'text', // 默认文字输入模式
        lastMessageId: 'msg-0',
        showMoreActionsPanel: false,
        userInfo: null,
        isRecording: false,
        recordingTime: 0,
        recordTimer: null,
        inputAreaHidden: false,
        inputModeBeforeHidden: 'text', // 记录隐藏前的输入模式
        inputFocused: false,
        isFocused: false,
        keyboardHeight: 0,
        scrollViewHeight: 0,
        scrollTop: 0,
        inputValue: '',
        robotConfig: null,
        robotPersonality: {
            friendly: 0.8,
            professional: 0.7,
            humorous: 0.6
        },
        networkError: null,
        isLoading: false,
        retryCount: 0,
        errorLogUploadTask: null,
        pageLoadTimestamp: 0,
        systemRecommendation: '',
        userInteractionStats: null,
        userProfile: null,
        interactionStrategy: null,
        personalizedRecommendations: null,
        recommendationFeedbackStats: null,
        intelligentRecommendations: null,
        recommendationId: null,
        interactionStyle: null,
        communicationTone: null,
        recommendationConfidence: null,
        securityStatus: {
            riskLevel: 'low',
            anomalyScore: 0,
            securityToken: null
        },
        currentLocation: null,
        conversationHistory: [],
        currentTask: null,
        aiAssistantMode: true  // 是否开启AI辅助模式
    },

    onLoad: function(options) {
        // 记录页面加载时间戳
        this.data.pageLoadTimestamp = Date.now();

        // 检查用户登录状态
        const userInfo = wx.getStorageSync('userInfo');
        if (!userInfo) {
            wx.redirectTo({
                url: '/pages/login/login'
            });
            return;
        }

        // 调试头像路径
        console.log('User Avatar URL:', userInfo.avatarUrl);
        console.log('Default User Avatar:', '/images/profile_icon.png');
        console.log('Default AI Avatar:', '/images/ai-avatar.png');

        // 更新用户信息
        this.setData({
            userInfo: userInfo,
            aiAssistant: {
                avatar: '/images/ai-avatar.png'
            }
        });

        // 如果没有头像，使用默认头像
        if (!userInfo.avatarUrl) {
            userInfo.avatarUrl = '/images/profile_icon.png';
        }

        // 计算初始滚动区域高度
        const systemInfo = wx.getSystemInfoSync();
        const screenHeight = systemInfo.windowHeight;
        const scrollViewHeight = screenHeight - 200; // 预留头部和底部空间

        this.setData({
            scrollViewHeight: scrollViewHeight
        });

        // 加载机器人配置
        wx.request({
            url: `${API_CONFIG[CURRENT_ENV]}/api/ai/config/`,
            success: (res) => {
                if (res.statusCode === 200) {
                    this.setData({
                        robotConfig: res.data
                    });
                }
            }
        });

        // 启动错误日志上传任务
        this.startErrorLogUploadTask();

        // 上报页面加载事件
        wx.reportAnalytics('page_load', {
            page: 'index',
            timestamp: this.data.pageLoadTimestamp
        });

        // 获取用户交互洞察
        this.fetchUserInteractionInsights();

        // 获取个性化推荐
        this.fetchPersonalizedRecommendations();

        // 安全智能系统初始化
        const securityInitPromise = SecurityModule.initializeSecurity(this);
        
        // 处理安全初始化结果
        securityInitPromise
            .then(riskLevel => {
                console.log('安全模块初始化成功', riskLevel);
                
                // 根据风险等级采取不同策略
                switch(riskLevel) {
                    case 'high':
                        wx.showModal({
                            title: '安全警告',
                            content: '检测到较高的安全风险，建议谨慎操作',
                            confirmText: '了解',
                            showCancel: false
                        });
                        break;
                    case 'medium':
                        wx.showToast({
                            title: '安全风险提醒',
                            icon: 'none'
                        });
                        break;
                }
            })
            .catch(error => {
                console.error('安全模块初始化失败', error);
                wx.showToast({
                    title: '安全检查异常',
                    icon: 'none'
                });
            });

        // 网络连接问题：添加网络状态检查
        wx.getNetworkType({
            success: (res) => {
                const networkType = res.networkType;
                if (networkType === 'none') {
                    wx.showToast({
                        title: '网络已断开',
                        icon: 'none'
                    });
                }
            }
        });

        // 增加图片预加载机制
        this.preloadAvatars();

        // 初始化AI助手
        this.initAIAssistant();
    },

    initAIAssistant() {
        robotService.initializeRobotContext({
            onTaskCreated: this.handleAIGeneratedTask.bind(this),
            onConversationUpdate: this.updateConversationHistory.bind(this)
        });
    },

    // AI智能下单流程
    startAIOrderAssistant() {
        wx.showModal({
            title: '智能下单助手',
            content: '我将帮助你快速创建跑腿订单。请描述你需要完成的任务。',
            confirmText: '开始',
            success: () => {
                this.setData({ aiAssistantMode: true });
                this.startVoiceInput();
            }
        });
    },

    // 语音输入
    startVoiceInput() {
        wx.startRecord({
            success: (res) => {
                const tempFilePath = res.tempFilePath;
                robotService.transcribeVoice(tempFilePath)
                    .then(this.processVoiceCommand.bind(this));
            }
        });
    },

    // 处理语音命令
    processVoiceCommand(text) {
        robotService.analyzeUserIntent(text)
            .then(intent => {
                switch(intent.type) {
                    case 'delivery_order':
                        this.createDeliveryOrder(intent.details);
                        break;
                    case 'errand':
                        this.createErrandOrder(intent.details);
                        break;
                    case 'query':
                        this.handleUserQuery(intent.details);
                        break;
                    default:
                        this.requestMoreDetails(text);
                }
            });
    },

    // 创建快递订单
    createDeliveryOrder(details) {
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/orders/delivery`,
            method: 'POST',
            data: {
                ...details,
                userLocation: this.data.currentLocation
            },
            success: (res) => {
                wx.showToast({ title: '快递订单创建成功' });
                this.setData({ currentTask: res.data.order });
            }
        });
    },

    // 创建跑腿订单
    createErrandOrder(details) {
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/orders/errand`,
            method: 'POST',
            data: {
                ...details,
                userLocation: this.data.currentLocation
            },
            success: (res) => {
                wx.showToast({ title: '跑腿订单创建成功' });
                this.setData({ currentTask: res.data.order });
            }
        });
    },

    // AI处理用户查询
    handleUserQuery(query) {
        robotService.processQuery(query)
            .then(response => {
                wx.showModal({
                    title: '查询结果',
                    content: response.answer,
                    showCancel: false
                });
            });
    },

    // 请求更多详细信息
    requestMoreDetails(originalText) {
        robotService.generateFollowupQuestions(originalText)
            .then(questions => {
                wx.showActionSheet({
                    itemList: questions,
                    success: (res) => {
                        // 根据选择的问题继续对话
                        this.continueConversation(questions[res.tapIndex]);
                    }
                });
            });
    },

    // 继续对话
    continueConversation(followupQuestion) {
        robotService.processFollowup(followupQuestion)
            .then(this.processVoiceCommand.bind(this));
    },

    // 更新对话历史
    updateConversationHistory(message) {
        const updatedHistory = [...this.data.conversationHistory, message];
        this.setData({ conversationHistory: updatedHistory });
    },

    // AI生成任务处理
    handleAIGeneratedTask(task) {
        wx.showModal({
            title: 'AI任务助手',
            content: `我为你生成了一个任务：${task.description}`,
            confirmText: '确认',
            cancelText: '修改',
            success: () => {
                if (res.confirm) {
                    this.createErrandOrder(task);
                }
            }
        });
    },

    // 获取推荐跑腿员
    getRecommendedRunners() {
        robotService.recommendRunners(this.data.currentLocation)
            .then(runners => {
                wx.showActionSheet({
                    itemList: runners.map(runner => `${runner.name} (${runner.rating}星)`),
                    success: (res) => {
                        // 选择跑腿员
                        const selectedRunner = runners[res.tapIndex];
                        this.assignRunnerToOrder(selectedRunner);
                    }
                });
            });
    },

    // 分配跑腿员
    assignRunnerToOrder(runner) {
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/orders/assign`,
            method: 'POST',
            data: {
                orderId: this.data.currentTask.id,
                runnerId: runner.id
            }
        });
    },

    onShow() {
        if (this.data.aiAssistantMode) {
            robotService.resumeContext();
        }
    },

    toggleInputMode() {
        this.setData({
            inputMode: this.data.inputMode === 'text' ? 'voice' : 'text'
        });
    },

    startVoiceRecord() {
        const self = this;
        this.setData({
            isRecording: true,
            recordingTime: 0
        });

        this.data.recordTimer = setInterval(() => {
            const newTime = this.data.recordingTime + 1;
            if (newTime > 60) {
                this.stopVoiceRecord();
                return;
            }
            this.setData({
                recordingTime: newTime
            });
        }, 1000);

        wx.startRecord({
            success: (res) => {
                const tempFilePath = res.tempFilePath;
                this.uploadVoiceAndTranscribe(tempFilePath);
            },
            fail: (err) => {
                wx.showToast({
                    title: '录音失败',
                    icon: 'none'
                });
                this.cancelVoiceRecord();
            }
        });
    },

    stopVoiceRecord() {
        wx.stopRecord();
        this.cancelVoiceRecord();
    },

    cancelVoiceRecord() {
        clearInterval(this.data.recordTimer);
        this.setData({
            isRecording: false,
            recordingTime: 0
        });
    },

    uploadVoiceAndTranscribe(tempFilePath) {
        wx.showLoading({ title: '识别中' });
        setTimeout(() => {
            wx.hideLoading();
            const transcribedText = '这是一段语音识别的文字';
            this.sendMessage(transcribedText);
            this.cancelVoiceRecord();
        }, 1000);
    },

    onInputFocus(e) {
        // 获取输入框焦点时的处理
        wx.createSelectorQuery().select('.chat-content').boundingClientRect((rect) => {
            this.setData({
                inputFocused: true,
                scrollTop: 99999, // 滚动到底部
                inputValue: '' // 清空输入框内容
            });
        }).exec();
    },

    onInputBlur() {
        // 失去焦点时的处理
        this.setData({
            inputFocused: false
        });
    },

    onInputChange(e) {
        const inputMessage = e.detail.value || '';
        this.setData({
            inputMessage: inputMessage
        });
    },

    onKeyboardHeightChange(e) {
        const { height } = e.detail;
        this.setData({
            keyboardHeight: height
        });
    },

    handleInputTap() {
        wx.hideKeyboard(); // 如果需要手动控制键盘
    },

    handleInput(e) {
        this.setData({
            inputValue: e.detail.value
        });
    },

    sendMessage(customMessage) {
        const userMessage = customMessage.detail ? 
            (customMessage.detail.value || this.data.inputMessage) : 
            (customMessage || this.data.inputMessage || '');
        
        if (typeof userMessage !== 'string' || !userMessage.trim()) return;

        // 重置重试计数
        this.setData({ retryCount: 0 });

        // 添加用户消息到聊天列表
        const newUserMessage = {
            type: 'user',
            content: userMessage,
            id: `msg-${Date.now()}`,
            timestamp: Date.now()
        };

        this.setData({
            messages: [...this.data.messages, newUserMessage],
            inputMessage: '',
            networkError: null,
            isLoading: true
        });

        // 发送消息到后端
        this.sendMessageToAPI(userMessage);
        
        // 调用智能交互助手
        this.intelligentInteractionAssistant({
            type: 'message_send',
            content: userMessage,
            timestamp: Date.now()
        });

        // 上传交互分析数据
        this.uploadInteractionAnalytics({
            type: 'message_send',
            content: userMessage,
            timestamp: Date.now()
        });
    },

    sendMessageToAPI(userMessage, apiUrl = API_BASE_URL) {
        // 显示加载动画
        wx.showLoading({
            title: '正在处理...',
            mask: true
        });

        return new Promise((resolve, reject) => {
            wx.request({
                url: `${apiUrl}/api/process_message`,
                method: 'POST',
                data: userMessage,
                header: {
                    'Content-Type': 'application/json'
                },
                success: (res) => {
                    // 隐藏加载动画
                    wx.hideLoading();

                    if (res.statusCode === 200) {
                        resolve(res.data);
                    } else {
                        reject(new Error(`API请求失败: ${res.statusCode}`));
                    }
                },
                fail: (error) => {
                    // 隐藏加载动画
                    wx.hideLoading();
                    
                    reject(error);
                }
            });
        });
    },

    handleAPIError(userMessage, res) {
        const errorMessage = {
            type: 'ai',
            content: '抱歉，服务器出现问题，请稍后再试。',
            id: `msg-${Date.now()}`,
            timestamp: Date.now()
        };
        this.setData({
            messages: [...this.data.messages, errorMessage],
            networkError: res,
            isLoading: false
        });

        // 尝试其他 API 地址
        this.tryAlternativeAPIs(userMessage);
    },

    handleNetworkError(userMessage, error) {
        // 记录网络错误详情
        const errorLog = {
            timestamp: new Date().toISOString(),
            userMessage: userMessage,
            errorType: error.errMsg || 'Unknown Network Error',
            networkStatus: null
        };

        // 检查网络状态
        wx.getNetworkType({
            success: (res) => {
                errorLog.networkStatus = res.networkType;
            },
            complete: () => {
                // 存储错误日志（可以考虑上传到服务器）
                const errorLogs = wx.getStorageSync('networkErrorLogs') || [];
                errorLogs.push(errorLog);
                
                // 限制错误日志数量
                if (errorLogs.length > 50) {
                    errorLogs.shift();
                }
                
                wx.setStorageSync('networkErrorLogs', errorLogs);

                // 显示友好的错误提示
                wx.showToast({
                    title: '网络连接异常，正在尝试重新连接',
                    icon: 'none',
                    duration: 2000
                });

                // 更新界面状态
                this.setData({
                    networkError: errorLog,
                    messages: [
                        ...this.data.messages,
                        { 
                            type: 'system', 
                            content: `网络错误：${errorLog.errorType}。正在尝试重新连接...`,
                            timestamp: errorLog.timestamp
                        }
                    ]
                });

                // 尝试备选 API
                this.tryAlternativeAPIs(userMessage);
            }
        });
    },

    tryAlternativeAPIs(userMessage) {
        const alternativeAPIs = [
            { 
                url: `${API_CONFIG.local}/api/process_message`,
                name: 'Local Django Server'
            },
            { 
                url: `${API_CONFIG.development}/api/process_message`,
                name: 'Development Django Server'
            },
            { 
                url: `${API_CONFIG.alternative1}/api/process_message`,
                name: 'Alternative Server 1'
            },
            { 
                url: `${API_CONFIG.alternative2}/api/process_message`,
                name: 'Alternative Server 2'
            }
        ];

        // 增加重试计数
        this.setData({
            retryCount: (this.data.retryCount || 0) + 1
        });

        // 如果重试次数过多，切换到模拟响应
        if (this.data.retryCount > 3) {
            wx.showToast({
                title: '网络异常，切换到模拟模式',
                icon: 'none',
                duration: 2000
            });
            return this.simulateAIResponse(userMessage);
        }

        // 顺序尝试备选 API
        const tryNextAPI = (apiIndex = 0) => {
            if (apiIndex >= alternativeAPIs.length) {
                // 所有 API 都尝试失败
                wx.showToast({
                    title: '网络连接失败，请稍后重试',
                    icon: 'none',
                    duration: 2000
                });
                
                this.setData({
                    networkError: {
                        message: '所有备选服务器都无法连接',
                        timestamp: new Date().toISOString()
                    }
                });

                return this.simulateAIResponse(userMessage);
            }

            const currentAPI = alternativeAPIs[apiIndex];

            wx.request({
                url: currentAPI.url,
                method: 'POST',
                data: {
                    message: userMessage,
                    user_id: this.data.userInfo ? this.data.userInfo.nickName : 'anonymous',
                    platform: 'miniprogram'
                },
                header: {
                    'Content-Type': 'application/json'
                },
                timeout: 10000, // 10秒超时
                success: (res) => {
                    if (res.statusCode === 200 && res.data.status === 'success') {
                        wx.showToast({
                            title: `成功连接到 ${currentAPI.name}`,
                            icon: 'none',
                            duration: 1500
                        });

                        // 重置重试计数
                        this.setData({
                            retryCount: 0
                        });

                        // 处理响应
                        const aiResponse = res.data.response;
                        const aiIntent = res.data.intent;

                        this.setData({
                            messages: [
                                ...this.data.messages,
                                { 
                                    id: `msg-${Date.now()}`, 
                                    type: 'ai', 
                                    content: aiResponse,
                                    intent: aiIntent,
                                    timestamp: new Date().toISOString(),
                                    source: currentAPI.name
                                }
                            ],
                            scrollTop: 9999
                        });

                        // 处理特殊意图
                        if (aiIntent) {
                            this.handleSpecialIntents(aiIntent);
                        }
                    } else {
                        // 当前 API 失败，尝试下一个
                        tryNextAPI(apiIndex + 1);
                    }
                },
                fail: () => {
                    // 当前 API 失败，尝试下一个
                    tryNextAPI(apiIndex + 1);
                }
            });
        };

        // 开始尝试备选 API
        tryNextAPI();
    },

    simulateAIResponse(userInput) {
        // 分析用户意图
        wx.request({
            url: 'http://localhost:8000/api/analyze_message',
            method: 'POST',
            data: {
                message: userInput,
                context: this.data.messages.slice(-5).map(msg => ({
                    type: msg.type,
                    content: msg.content
                }))
            },
            success: (res) => {
                const aiResponse = {
                    type: 'ai',
                    content: res.data.response,
                    id: `msg-${Date.now()}`,
                    timestamp: Date.now()
                };

                const newMessages = [...this.data.messages, aiResponse];
                
                this.setData({
                    messages: newMessages,
                    lastMessageId: `msg-${newMessages.length - 1}`
                });

                // 根据意图处理特殊响应
                this.handleSpecialIntents(res.data.intent);

                // 根据用户交互动态调整机器人个性
                this.adjustRobotPersonality(res.data.intent);
            },
            fail: (error) => {
                const errorMessage = {
                    type: 'ai',
                    content: '抱歉，我暂时无法回复您的消息。'
                };
                
                const newMessages = [...this.data.messages, errorMessage];
                
                this.setData({
                    messages: newMessages,
                    lastMessageId: `msg-${newMessages.length - 1}`
                });
            }
        });
    },

    handleSpecialIntents(intent) {
        switch(intent) {
            case '地址保存':
                wx.showToast({
                    title: '地址已保存',
                    icon: 'success'
                });
                break;
            case '订单查询':
                wx.showToast({
                    title: '订单信息已查询',
                    icon: 'info'
                });
                break;
        }
    },

    adjustRobotPersonality(interaction) {
        // 根据用户交互动态调整机器人个性
        const personalityAdjustments = {
            '友好': { friendly: 0.1 },
            '专业': { professional: 0.1 },
            '幽默': { humorous: 0.1 }
        };

        const adjustment = personalityAdjustments[interaction] || {};
        
        this.setData({
            robotPersonality: {
                ...this.data.robotPersonality,
                ...Object.fromEntries(
                    Object.entries(adjustment).map(([key, value]) => 
                        [key, Math.min(1, this.data.robotPersonality[key] + value)]
                    )
                )
            }
        });
    },

    showMoreActions() {
        if (!this.data.showMoreActionsPanel) {
            this.setData({
                inputModeBeforeHidden: this.data.inputMode,
                inputMode: 'text',
                showMoreActionsPanel: true,
                inputFocused: false
            });
        } else {
            this.setData({
                showMoreActionsPanel: false,
                inputMode: this.data.inputModeBeforeHidden,
                inputFocused: true
            });
        }
    },

    handleChatAreaTap() {
        if (this.data.showMoreActionsPanel) {
            this.setData({
                showMoreActionsPanel: false,
                inputMode: this.data.inputModeBeforeHidden,
                inputFocused: true
            });
        }
    },

    recoverInputArea() {
        this.setData({
            inputAreaHidden: false,
            inputMode: 'text' // 直接切换到文字输入
        });
    },

    takePhoto() {
        wx.chooseMedia({
            count: 1,
            mediaType: ['image'],
            sourceType: ['camera'],
            success: (res) => {
                wx.showToast({
                    title: '照片已选择',
                    icon: 'success'
                });
                this.setData({ showMoreActionsPanel: false });
            }
        });
    },

    chooseFromAlbum() {
        wx.chooseMedia({
            count: 1,
            mediaType: ['image'],
            sourceType: ['album'],
            success: (res) => {
                wx.showToast({
                    title: '照片已选择',
                    icon: 'success'
                });
                this.setData({ showMoreActionsPanel: false });
            }
        });
    },

    shareLocation() {
        wx.chooseLocation({
            success: (res) => {
                wx.showToast({
                    title: '位置已选择',
                    icon: 'success'
                });
                this.setData({ showMoreActionsPanel: false });
            }
        });
    },

    goToProfile() {
        wx.navigateTo({
            url: '/pages/profile/profile'
        });
    },

    onMoreTap() {
        wx.showActionSheet({
            itemList: ['清空聊天', '导出聊天记录'],
            success: (res) => {
                switch(res.tapIndex) {
                    case 0:
                        this.clearMessages();
                        break;
                    case 1:
                        this.exportChat();
                        break;
                }
            }
        });
    },

    clearMessages() {
        this.setData({
            messages: [{ 
                type: 'ai', 
                content: '聊天记录已清空。' 
            }],
            lastMessageId: 'msg-0'
        });
    },

    exportChat() {
        const chatText = this.data.messages.map(msg => 
            `${msg.type === 'ai' ? '小六: ' : '我: '}${msg.content}`
        ).join('\n');

        wx.setClipboardData({
            data: chatText,
            success: () => {
                wx.showToast({
                    title: '聊天记录已复制',
                    icon: 'success'
                });
            }
        });
    },

    uploadNetworkErrorLogs() {
        const errorLogs = wx.getStorageSync('networkErrorLogs') || [];
        if (errorLogs.length === 0) return;

        // 检查网络状态
        wx.getNetworkType({
            success: (res) => {
                const networkType = res.networkType;
                if (networkType === 'none') {
                    console.log('网络不可用，暂不上传错误日志');
                    return;
                }

                // 尝试上传日志
                const uploadUrls = [
                    `${API_BASE_URL}/api/logs/network_errors/`
                ];

                const tryUploadLog = (urlIndex = 0) => {
                    if (urlIndex >= uploadUrls.length) {
                        console.log('无法上传网络错误日志');
                        return;
                    }

                    wx.request({
                        url: uploadUrls[urlIndex],
                        method: 'POST',
                        data: { logs: errorLogs },
                        header: {
                            'Content-Type': 'application/json'
                        },
                        success: (res) => {
                            if (res.statusCode === 200) {
                                // 上传成功，清空本地日志
                                wx.removeStorageSync('networkErrorLogs');
                                console.log('网络错误日志上传成功');
                            } else {
                                // 尝试下一个 URL
                                tryUploadLog(urlIndex + 1);
                            }
                        },
                        fail: (error) => {
                            console.error('上传网络错误日志失败', error);
                            // 尝试下一个 URL
                            tryUploadLog(urlIndex + 1);
                        }
                    });
                };

                tryUploadLog();
            },
            fail: (error) => {
                console.error('获取网络状态失败', error);
            }
        });
    },

    startErrorLogUploadTask() {
        // 每 30 分钟检查一次是否需要上传错误日志
        const uploadInterval = 30 * 60 * 1000; // 30 分钟

        const uploadTask = setInterval(() => {
            const errorLogs = wx.getStorageSync('networkErrorLogs') || [];
            
            // 如果错误日志数量超过 10 条，立即上传
            if (errorLogs.length >= 10) {
                this.uploadNetworkErrorLogs();
            }
        }, uploadInterval);

        // 保存定时器，以便后续可以取消
        this.data.errorLogUploadTask = uploadTask;
    },

    onUnload: function() {
        // 清理错误日志上传任务
        if (this.data.errorLogUploadTask) {
            clearInterval(this.data.errorLogUploadTask);
        }

        // 停止语音录制
        if (this.data.isRecording) {
            wx.stopRecord();
        }

        // 清理录音计时器
        if (this.data.recordTimer) {
            clearInterval(this.data.recordTimer);
        }

        // 尝试上传未上传的错误日志
        this.uploadNetworkErrorLogs();

        // 记录页面访问时长
        const pageVisitDuration = Date.now() - this.data.pageLoadTimestamp;
        wx.reportAnalytics('page_visit_duration', {
            duration: pageVisitDuration,
            page: 'index'
        });
    },

    onHide: function() {
        // 页面不可见时的处理
        // 暂停语音识别
        if (this.data.isRecording) {
            wx.pauseRecord();
        }

        // 保存当前会话状态
        wx.setStorageSync('lastConversationState', {
            messages: this.data.messages,
            timestamp: Date.now()
        });
    },

    onShow: function() {
        // 页面重新可见时的处理
        // 性能和用户体验监控
        const performanceMetrics = {
            pageShowTimestamp: Date.now(),
            memoryUsage: wx.getSystemInfoSync().memoryUsed,
            batteryLevel: wx.getBatteryInfoSync().level
        };

        // 恢复语音识别
        if (this.data.isRecording) {
            wx.resumeRecord();
        }

        // 检查是否有保存的会话状态
        const lastState = wx.getStorageSync('lastConversationState');
        if (lastState && Date.now() - lastState.timestamp < 24 * 60 * 60 * 1000) {
            // 如果状态保存在 24 小时内，恢复对话
            this.setData({
                messages: lastState.messages
            });

            // 性能日志：恢复会话
            wx.reportAnalytics('session_restore', {
                sessionAge: Date.now() - lastState.timestamp,
                messageCount: lastState.messages.length
            });
        }

        // 检查网络状态
        wx.getNetworkType({
            success: (res) => {
                const networkType = res.networkType;
                
                // 网络状态变更提醒
                if (networkType === 'none') {
                    wx.showToast({
                        title: '当前无网络连接',
                        icon: 'none'
                    });
                }

                // 上报网络状态
                wx.reportAnalytics('network_status', {
                    type: networkType,
                    timestamp: Date.now()
                });

                // 根据网络状态调整性能
                this.adjustPerformanceByNetwork(networkType);
            }
        });

        // 检查系统性能
        this.checkSystemPerformance();

        // 重新启动错误日志上传任务
        this.startErrorLogUploadTask();

        // 上报页面可见事件
        wx.reportAnalytics('page_show', {
            page: 'index',
            timestamp: performanceMetrics.pageShowTimestamp,
            memoryUsed: performanceMetrics.memoryUsage,
            batteryLevel: performanceMetrics.batteryLevel
        });
    },

    // 根据网络状态调整性能
    adjustPerformanceByNetwork(networkType) {
        switch(networkType) {
            case 'wifi':
                // 高性能模式
                this.setData({
                    robotPersonality: {
                        friendly: 0.9,
                        professional: 0.8,
                        responseDetail: 'high'
                    }
                });
                break;
            case '4g':
                // 平衡模式
                this.setData({
                    robotPersonality: {
                        friendly: 0.7,
                        professional: 0.6,
                        responseDetail: 'medium'
                    }
                });
                break;
            case '3g':
            case '2g':
                // 低性能模式
                this.setData({
                    robotPersonality: {
                        friendly: 0.5,
                        professional: 0.4,
                        responseDetail: 'low'
                    }
                });
                break;
            default:
                // 默认模式
                this.setData({
                    robotPersonality: {
                        friendly: 0.6,
                        professional: 0.5,
                        responseDetail: 'default'
                    }
                });
        }
    },

    // 系统性能检查
    checkSystemPerformance() {
        const systemInfo = wx.getSystemInfoSync();
        const performanceWarnings = [];

        // 内存使用检查
        if (systemInfo.memoryUsed / systemInfo.memoryTotal > 0.8) {
            performanceWarnings.push('高内存占用');
        }

        // 电池电量检查
        const batteryInfo = wx.getBatteryInfoSync();
        if (batteryInfo.level < 20) {
            performanceWarnings.push('电池电量低');
        }

        // 性能警告
        if (performanceWarnings.length > 0) {
            wx.showModal({
                title: '系统性能提醒',
                content: performanceWarnings.join('，') + '。建议优化使用。',
                showCancel: false
            });

            // 上报性能警告
            wx.reportAnalytics('performance_warning', {
                warnings: performanceWarnings,
                timestamp: Date.now()
            });
        }
    },
    
    // 智能交互助手
    intelligentInteractionAssistant(interaction) {
        // 分析用户交互模式
        const analysisResult = this.analyzeUserInteraction(interaction);

        // 根据分析结果调整交互策略
        this.adjustInteractionStrategy(analysisResult);

        // 个性化推荐
        this.generatePersonalizedRecommendations(analysisResult);

        // 上报交互分析
        wx.reportAnalytics('intelligent_interaction', {
            interactionType: interaction.type,
            sentiment: analysisResult.sentiment,
            complexity: analysisResult.complexity,
            timestamp: Date.now()
        });
    },

    // 分析用户交互
    analyzeUserInteraction(interaction) {
        // 安全检查：确保 interaction 是对象且包含文本
        if (!interaction || typeof interaction !== 'object') {
            console.warn('Invalid interaction data');
            return;
        }

        const text = interaction.text || '';

        // 情感分析
        const detectSentiment = (text) => {
            // 确保 text 是字符串
            if (typeof text !== 'string') {
                return 0; // 中性
            }

            const lowerText = text.toLowerCase();
            const positiveWords = sentimentMap.positive;
            const negativeWords = sentimentMap.negative;

            const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
            const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;

            // 计算情感得分 (-1 到 1)
            return (positiveCount - negativeCount) / (positiveCount + negativeCount + 1);
        };

        // 意图识别
        const identifyIntent = (text) => {
            const intents = {
                greeting: ['你好', '嗨', 'hi', 'hello'],
                question: ['吗', '?', '什么', 'how', 'why'],
                request: ['帮我', '请', 'help', 'can you'],
                command: ['做', '生成', 'create', 'generate']
            };

            for (const [intent, keywords] of Object.entries(intents)) {
                if (keywords.some(keyword => text.toLowerCase().includes(keyword))) {
                    return intent;
                }
            }

            return 'general';
        };

        // 复杂度计算
        const calculateComplexity = (text) => {
            // 基于词语数量和句子结构的复杂度评估
            const wordCount = text.split(/\s+/).length;
            const sentenceCount = text.split(/[。！？.!?]/).length;
            const avgSentenceLength = wordCount / sentenceCount;

            // 复杂度评分 (0-1)
            return Math.min(1, (avgSentenceLength / 10) * (wordCount / 20));
        };

        // 分析结果
        const analysisResult = {
            timestamp: Date.now(),
            userMessage: {
                text: text,
                length: text.length,
                sentiment: {
                    score: detectSentiment(text),
                    category: this.categorizeSentiment(detectSentiment(text))
                },
                complexity: calculateComplexity(text)
            },
            intent: identifyIntent(text)
        };

        // 记录分析结果
        this.uploadInteractionAnalytics(analysisResult);

        return analysisResult;
    },

    // 分类情感
    categorizeSentiment(sentimentScore) {
        if (sentimentScore > 0.7) return 'positive';
        if (sentimentScore < 0.3) return 'negative';
        return 'neutral';
    },

    // 调整交互策略
    adjustInteractionStrategy(analysisResult) {
        const strategyMap = {
            positive: {
                personality: {
                    friendly: 1.0,
                    professional: 0.7,
                    humorous: 0.9
                },
                responseStyle: 'enthusiastic'
            },
            negative: {
                personality: {
                    friendly: 0.4,
                    professional: 0.8,
                    humorous: 0.2
                },
                responseStyle: 'apologetic'
            },
            neutral: {
                personality: {
                    friendly: 0.6,
                    professional: 0.6,
                    humorous: 0.5
                },
                responseStyle: 'balanced'
            }
        };

        const strategy = strategyMap[analysisResult.sentiment.category] || strategyMap.neutral;

        this.setData({
            robotPersonality: {
                ...this.data.robotPersonality,
                ...strategy.personality
            },
            responseStyle: strategy.responseStyle
        });
    },

    // 个性化推荐
    generatePersonalizedRecommendations(analysisResult) {
        const recommendationCategories = {
            high_complexity: [
                '我注意到你的问题比较复杂，需要详细解答',
                '让我们更深入地讨论这个话题',
                '我可以帮你breakdown这个问题'
            ],
            low_complexity: [
                '看起来是个简单的问题，我可以快速帮你解决',
                '这个问题很直接，我们马上就能搞定'
            ],
            positive_sentiment: [
                '很高兴能帮到你！还有什么我可以协助的吗？',
                '你的反馈很棒，继续保持这种积极的态度'
            ],
            negative_sentiment: [
                '我理解你的frustration，让我们一起找解决方案',
                '对不起给你带来了不便，我们来改进'
            ]
        };

        let recommendations = [];

        if (analysisResult.userMessage.complexity === 'high') {
            recommendations.push(...recommendationCategories.high_complexity);
        } else if (analysisResult.userMessage.complexity === 'low') {
            recommendations.push(...recommendationCategories.low_complexity);
        }

        if (analysisResult.userMessage.sentiment.category === 'positive') {
            recommendations.push(...recommendationCategories.positive_sentiment);
        } else if (analysisResult.userMessage.sentiment.category === 'negative') {
            recommendations.push(...recommendationCategories.negative_sentiment);
        }

        // 随机选择一个推荐
        if (recommendations.length > 0) {
            const randomRecommendation = recommendations[Math.floor(Math.random() * recommendations.length)];
            
            this.setData({
                systemRecommendation: randomRecommendation
            });
        }
    },

    // 上传交互分析数据
    uploadInteractionAnalytics(interaction) {
        const userInfo = wx.getStorageSync('userInfo');
        
        // 准备上传的数据
        const analyticsData = {
            userId: userInfo.openId || 'anonymous',
            interactionType: interaction.type,
            content: interaction.content || '',
            timestamp: Date.now()
        };

        wx.request({
            url: `${API_CONFIG[CURRENT_ENV]}/api/interaction/analytics/`,
            method: 'POST',
            data: analyticsData,
            success: (res) => {
                if (res.statusCode === 200) {
                    // 处理分析结果
                    this.processInteractionAnalyticsResponse(res.data);
                } else {
                    // 记录上传失败的错误日志
                    this.recordNetworkErrorLog({
                        type: 'interaction_analytics_upload',
                        url: `${API_CONFIG[CURRENT_ENV]}/api/interaction/analytics/`,
                        statusCode: res.statusCode,
                        response: res.data
                    });
                }
            },
            fail: (error) => {
                // 记录网络错误
                this.recordNetworkErrorLog({
                    type: 'interaction_analytics_upload',
                    url: `${API_CONFIG[CURRENT_ENV]}/api/interaction/analytics/`,
                    error: error
                });
            }
        });
    },

    // 处理交互分析响应
    processInteractionAnalyticsResponse(responseData) {
        // 根据服务器返回的分析结果调整交互
        if (responseData.sentiment && responseData.complexity) {
            // 更新机器人个性
            const personalityAdjustments = {
                positive: { friendly: 0.9, professional: 0.7 },
                negative: { friendly: 0.4, professional: 0.8 },
                neutral: { friendly: 0.6, professional: 0.6 }
            };

            const complexityAdjustments = {
                high: { responseDetail: 'detailed' },
                medium: { responseDetail: 'balanced' },
                low: { responseDetail: 'concise' }
            };

            this.setData({
                robotPersonality: {
                    ...this.data.robotPersonality,
                    ...personalityAdjustments[responseData.sentiment],
                    ...complexityAdjustments[responseData.complexity]
                }
            });

            // 如果服务器返回用户聚类信息，可以进一步个性化
            if (responseData.userClusters) {
                this.handleUserClustering(responseData.userClusters);
            }
        }
    },

    // 处理用户交互聚类
    handleUserClustering(userClusters) {
        if (userClusters && userClusters.cluster_centers) {
            // 根据聚类中心调整交互策略
            const clusterStrategyMap = {
                0: { 
                    recommendationStyle: '技术导向',
                    interactionDetail: '深入专业' 
                },
                1: { 
                    recommendationStyle: '情感导向',
                    interactionDetail: '友好亲和' 
                },
                2: { 
                    recommendationStyle: '平衡型',
                    interactionDetail: '中立理性' 
                }
            };

            // 假设取第一个聚类结果
            const clusterIndex = userClusters.user_clusters[0];
            const clusterStrategy = clusterStrategyMap[clusterIndex];

            this.setData({
                interactionStrategy: clusterStrategy
            });
        }
    },

    // 获取用户交互洞察
    fetchUserInteractionInsights() {
        // 模拟用户交互洞察数据
        const mockInsightsData = {
            user_segments: ['active_learner', 'tech_enthusiast'],
            interaction_patterns: {
                avg_session_duration: 15.5,
                interaction_frequency: 'high',
                preferred_topics: ['AI', '技术创新', '个人成长']
            },
            recommendation_preferences: {
                content_types: ['article', 'video', 'interactive_guide'],
                difficulty_level: 'intermediate'
            },
            timestamp: Date.now()
        };

        try {
            wx.getNetworkType({
                success: (res) => {
                    if (res.networkType === 'none') {
                        // 网络不可用，使用模拟数据
                        this.processUserInteractionInsights(mockInsightsData);
                        wx.showToast({
                            title: '使用模拟交互数据',
                            icon: 'none',
                            duration: 2000
                        });
                        return;
                    }

                    // 尝试真实的 API 调用
                    wx.request({
                        url: `${API_CONFIG[CURRENT_ENV]}/api/interaction/insights/`,
                        method: 'GET',
                        header: {
                            'Authorization': `Bearer ${wx.getStorageSync('userToken')}`
                        },
                        success: (res) => {
                            if (res.statusCode === 200 && res.data) {
                                this.processUserInteractionInsights(res.data);
                            } else {
                                // API 返回异常，使用模拟数据
                                this.processUserInteractionInsights(mockInsightsData);
                                wx.showToast({
                                    title: '使用模拟交互数据',
                                    icon: 'none',
                                    duration: 2000
                                });
                            }
                        },
                        fail: () => {
                            // 网络请求失败，使用模拟数据
                            this.processUserInteractionInsights(mockInsightsData);
                            wx.showToast({
                                title: '使用模拟交互数据',
                                icon: 'none',
                                duration: 2000
                            });
                        }
                    });
                },
                fail: () => {
                    // 网络状态检查失败，使用模拟数据
                    this.processUserInteractionInsights(mockInsightsData);
                    wx.showToast({
                        title: '使用模拟交互数据',
                        icon: 'none',
                        duration: 2000
                    });
                }
            });
        } catch (error) {
            console.error('获取用户交互洞察失败', error);
            this.processUserInteractionInsights(mockInsightsData);
        }
    },

    // 处理用户交互洞察
    processUserInteractionInsights(insightsData) {
        if (insightsData.user_segments) {
            // 更新用户画像
            this.setData({
                userProfile: {
                    user_segments: insightsData.user_segments,
                    interaction_patterns: insightsData.interaction_patterns,
                    recommendation_preferences: insightsData.recommendation_preferences
                }
            });
        }

        if (insightsData.interaction_patterns) {
            // 更新用户交互统计
            this.setData({
                userInteractionStats: {
                    avg_session_duration: insightsData.interaction_patterns.avg_session_duration,
                    interaction_frequency: insightsData.interaction_patterns.interaction_frequency,
                    preferred_topics: insightsData.interaction_patterns.preferred_topics
                }
            });
        }

        if (insightsData.recommendation_preferences) {
            // 更新个性化推荐
            this.setData({
                personalizedRecommendations: {
                    content_types: insightsData.recommendation_preferences.content_types,
                    difficulty_level: insightsData.recommendation_preferences.difficulty_level
                }
            });
        }
    },

    // 获取个性化推荐
    fetchPersonalizedRecommendations() {
        // 模拟个性化推荐数据
        const mockRecommendations = {
            recommendation_id: `rec_${Date.now()}`,
            recommendation: {
                recommended_topics: [
                    '人工智能前沿技术',
                    '个人效率提升',
                    '创新思维训练'
                ],
                interaction_style: 'supportive',
                communication_tone: 'professional_friendly',
                recommendation_score: 0.85
            }
        };

        try {
            wx.getNetworkType({
                success: (res) => {
                    if (res.networkType === 'none') {
                        // 网络不可用，使用模拟数据
                        this.processPersonalizedRecommendations(mockRecommendations);
                        wx.showToast({
                            title: '使用模拟推荐数据',
                            icon: 'none',
                            duration: 2000
                        });
                        return;
                    }

                    // 尝试真实的 API 调用
                    wx.request({
                        url: `${API_CONFIG[CURRENT_ENV]}/api/recommendations/`,
                        method: 'GET',
                        header: {
                            'Authorization': `Bearer ${wx.getStorageSync('userToken')}`
                        },
                        success: (res) => {
                            if (res.statusCode === 200 && res.data) {
                                this.processPersonalizedRecommendations(res.data);
                            } else {
                                // API 返回异常，使用模拟数据
                                this.processPersonalizedRecommendations(mockRecommendations);
                                wx.showToast({
                                    title: '使用模拟推荐数据',
                                    icon: 'none',
                                    duration: 2000
                                });
                            }
                        },
                        fail: () => {
                            // 网络请求失败，使用模拟数据
                            this.processPersonalizedRecommendations(mockRecommendations);
                            wx.showToast({
                                title: '使用模拟推荐数据',
                                icon: 'none',
                                duration: 2000
                            });
                        }
                    });
                },
                fail: () => {
                    // 网络状态检查失败，使用模拟数据
                    this.processPersonalizedRecommendations(mockRecommendations);
                    wx.showToast({
                        title: '使用模拟推荐数据',
                        icon: 'none',
                        duration: 2000
                    });
                }
            });
        } catch (error) {
            console.error('获取个性化推荐失败', error);
            this.processPersonalizedRecommendations(mockRecommendations);
        }
    },

    // 处理个性化推荐
    processPersonalizedRecommendations(recommendations) {
        if (recommendations.recommendation) {
            // 更新个性化推荐
            this.setData({
                personalizedRecommendations: {
                    recommendation_id: recommendations.recommendation_id,
                    recommended_topics: recommendations.recommendation.recommended_topics,
                    interaction_style: recommendations.recommendation.interaction_style,
                    communication_tone: recommendations.recommendation.communication_tone,
                    recommendation_score: recommendations.recommendation.recommendation_score
                }
            });

            // 显示推荐主题
            if (recommendations.recommendation.recommended_topics.length > 0) {
                const randomTopic = recommendations.recommendation.recommended_topics[
                    Math.floor(Math.random() * recommendations.recommendation.recommended_topics.length)
                ];

                this.setData({
                    systemRecommendation: `我们可以聊聊：${randomTopic}`
                });
            }
        }
    },

    // 提交推荐反馈
    submitRecommendationFeedback(feedbackData) {
        wx.request({
            url: `${API_CONFIG[CURRENT_ENV]}/api/recommendations/feedback/`,
            method: 'POST',
            header: {
                'Authorization': `Bearer ${wx.getStorageSync('userToken')}`,
                'Content-Type': 'application/json'
            },
            data: {
                recommendation_id: this.data.personalizedRecommendations.recommendation_id,
                cluster: this.data.personalizedRecommendations.cluster,
                recommended_topics: this.data.personalizedRecommendations.recommended_topics,
                interaction_style: this.data.robotPersonality.interaction_style,
                feedback_type: feedbackData.feedbackType,
                interaction_result: feedbackData.interactionResult,
                additional_comments: feedbackData.additionalComments || ''
            },
            success: (res) => {
                if (res.statusCode === 200 && res.data.status === 'success') {
                    // 处理反馈成功
                    wx.showToast({
                        title: '感谢您的反馈！',
                        icon: 'success',
                        duration: 2000
                    });

                    // 可以根据反馈统计调整推荐策略
                    this.processFeedbackStats(res.data.feedback_stats);
                } else {
                    // 记录错误日志
                    this.recordNetworkErrorLog({
                        type: 'recommendation_feedback_submit',
                        url: `${API_CONFIG[CURRENT_ENV]}/api/recommendations/feedback/`,
                        statusCode: res.statusCode,
                        response: res.data
                    });
                }
            },
            fail: (error) => {
                // 记录网络错误
                this.recordNetworkErrorLog({
                    type: 'recommendation_feedback_submit',
                    url: `${API_CONFIG[CURRENT_ENV]}/api/recommendations/feedback/`,
                    error: error
                });
            }
        });
    },

    // 处理反馈统计
    processFeedbackStats(feedbackStats) {
        // 分析反馈统计，可能调整推荐策略
        const helpfulnessRatio = (
            (feedbackStats.feedback_distribution['helpful'] || 0) + 
            (feedbackStats.feedback_distribution['somewhat_helpful'] || 0)
        ) / feedbackStats.total_feedbacks;

        if (helpfulnessRatio < 0.5) {
            // 如果反馈不够正面，可以触发推荐策略调整
            this.fetchPersonalizedRecommendations();
        }

        // 更新本地反馈统计显示
        this.setData({
            recommendationFeedbackStats: feedbackStats
        });
    },

    // 获取推荐解释
    getRecommendationExplanation(recommendationId) {
        wx.showLoading({ title: '获取解释中...' });
        
        wx.request({
            url: `${this.data.apiBaseUrl}/recommendations/explanation/`,
            method: 'GET',
            header: {
                'Authorization': `Bearer ${wx.getStorageSync('token')}`
            },
            data: {
                recommendation_id: recommendationId
            },
            success: (res) => {
                wx.hideLoading();
                
                if (res.data.status === 'success') {
                    const explanation = res.data.recommendation_explanation;
                    
                    // 弹出详细解释对话框
                    wx.showModal({
                        title: '推荐解释',
                        content: this.formatRecommendationExplanation(explanation),
                        showCancel: false,
                        confirmText: '了解',
                        success: () => {
                            // 可以进一步处理，如保存解释或分享
                            this.saveRecommendationExplanation(explanation);
                        }
                    });
                } else {
                    wx.showToast({
                        title: '获取解释失败',
                        icon: 'none',
                        duration: 2000
                    });
                }
            },
            fail: (err) => {
                wx.hideLoading();
                wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                    duration: 2000
                });
                console.error('推荐解释获取失败', err);
            }
        });
    },

    // 格式化推荐解释
    formatRecommendationExplanation(explanation) {
        let explanationText = '推荐详细解释：\n\n';
        
        // 推荐主题解释
        if (explanation.detailed_explanation && explanation.detailed_explanation.topic_explanations) {
            explanationText += '推荐主题:\n';
            explanation.detailed_explanation.topic_explanations.forEach(topic => {
                explanationText += `- ${topic.topic}: ${topic.explanation} (相关性: ${(topic.relevance_score * 100).toFixed(0)}%)\n`;
            });
        }
        
        // 交互分析
        if (explanation.detailed_explanation && explanation.detailed_explanation.interaction_analysis) {
            const analysis = explanation.detailed_explanation.interaction_analysis;
            explanationText += `\n交互分析:\n`;
            explanationText += `主导交互类型: ${analysis.dominant_interaction_type}\n`;
            explanationText += `主导情感: ${analysis.dominant_sentiment}\n`;
            explanationText += `交互频率: ${analysis.interaction_frequency}\n`;
        }
        
        // 特征重要性
        if (explanation.feature_importance) {
            explanationText += '\n特征重要性:\n';
            Object.entries(explanation.feature_importance).forEach(([category, features]) => {
                explanationText += `${category}:\n`;
                Object.entries(features).forEach(([feature, importance]) => {
                    explanationText += `  - ${feature}: ${(importance * 100).toFixed(0)}%\n`;
                });
            });
        }
        
        // 推荐置信度
        if (explanation.confidence_score) {
            explanationText += `\n推荐置信度: ${(explanation.confidence_score * 100).toFixed(0)}%`;
        }
        
        return explanationText;
    },

    // 保存推荐解释
    saveRecommendationExplanation(explanation) {
        const savedExplanations = wx.getStorageSync('recommendation_explanations') || [];
        
        savedExplanations.push({
            timestamp: new Date().toISOString(),
            explanation: explanation
        });
        
        wx.setStorageSync('recommendation_explanations', savedExplanations);
    },

    // 获取模型可视化数据
    getModelVisualization() {
        wx.showLoading({ title: '加载模型可视化...' });
        
        wx.request({
            url: `${this.data.apiBaseUrl}/recommendations/model/visualization/`,
            method: 'GET',
            header: {
                'Authorization': `Bearer ${wx.getStorageSync('token')}`
            },
            success: (res) => {
                wx.hideLoading();
                
                if (res.data.status === 'success') {
                    const modelVisualization = res.data.model_visualization;
                    
                    // 显示模型可视化对话框
                    wx.showModal({
                        title: '推荐模型可视化',
                        content: this.formatModelVisualization(modelVisualization),
                        showCancel: false,
                        confirmText: '了解'
                    });
                } else {
                    wx.showToast({
                        title: '模型可视化加载失败',
                        icon: 'none',
                        duration: 2000
                    });
                }
            },
            fail: (err) => {
                wx.hideLoading();
                wx.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                    duration: 2000
                });
                console.error('模型可视化获取失败', err);
            }
        });
    },

    // 格式化模型可视化数据
    formatModelVisualization(visualization) {
        let visualizationText = '推荐模型可视化：\n\n';
        
        // 模型架构
        if (visualization.model_architecture) {
            visualizationText += '模型架构:\n';
            visualizationText += visualization.model_architecture + '\n\n';
        }
        
        // 层激活情况
        if (visualization.layer_activations) {
            visualizationText += '层激活情况:\n';
            Object.entries(visualization.layer_activations).forEach(([layer, activation]) => {
                visualizationText += `  ${layer}: 激活强度分析\n`;
            });
        }
        
        // 特征分布
        if (visualization.feature_distributions) {
            visualizationText += '\n特征分布:\n';
            Object.entries(visualization.feature_distributions).forEach(([feature, distribution]) => {
                visualizationText += `  ${feature}: 分布分析\n`;
            });
        }
        
        return visualizationText;
    },
    
    // 获取个性化洞察
    getPersonalizedInsights() {
      wx.showLoading({ title: '获取个人洞察...' });
      
      wx.request({
        url: `${this.data.apiBaseUrl}/insights/personalized/`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        success: (res) => {
          wx.hideLoading();
          
          if (res.data.status === 'success') {
            const insights = res.data.personalized_insights;
            
            // 创建洞察展示页面
            wx.navigateTo({
              url: '/pages/personalized_insights/personalized_insights',
              success: function(navigateRes) {
                // 通过eventChannel向目标页面传递数据
                navigateRes.eventChannel.emit('acceptDataFromOpenerPage', {
                  data: insights
                });
              }
            });
          } else {
            wx.showToast({
              title: '获取洞察失败',
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none',
            duration: 2000
          });
          console.error('个性化洞察获取失败', err);
        }
      });
    },
    
    // 保存并分享个性化洞察
    sharePersonalizedInsights(insights) {
      // 生成分享文案
      const shareText = insights.map(insight => {
        const keyTakeaways = insight.insights.key_takeaways.join('；');
        const learingSuggestions = insight.insights.learning_suggestions.join('；');
        const growthRecommendations = insight.insights.growth_recommendations.join('；');
        
        return `
个性化洞察 (${new Date(insight.timestamp).toLocaleString()}):
关键发现：${keyTakeaways}
学习建议：${learingSuggestions}
成长推荐：${growthRecommendations}
        `;
      }).join('\n\n');
      
      // 调用微信分享
      wx.shareTextToFriends({
        text: shareText,
        success: () => {
          wx.showToast({
            title: '分享成功',
            icon: 'success',
            duration: 2000
          });
        },
        fail: (err) => {
          wx.showToast({
            title: '分享失败',
            icon: 'none',
            duration: 2000
          });
          console.error('分享失败', err);
        }
      });
    },
    
    // 获取用户交互分析
    getUserInteractionAnalysis() {
      wx.showLoading({ title: '获取交互分析...' });
      
      wx.request({
        url: `${this.data.apiBaseUrl}/interaction/analysis/`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        success: (res) => {
          wx.hideLoading();
          
          if (res.data.status === 'success') {
            const interactionAnalysis = res.data.interaction_analysis;
            
            // 创建交互分析展示页面
            wx.navigateTo({
              url: '/pages/interaction_analysis/interaction_analysis',
              success: function(navigateRes) {
                navigateRes.eventChannel.emit('acceptDataFromOpenerPage', {
                  data: interactionAnalysis
                });
              }
            });
          } else {
            wx.showToast({
              title: '获取交互分析失败',
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none',
            duration: 2000
          });
          console.error('交互分析获取失败', err);
        }
      });
    },
    
    // 获取交互适配策略
    getInteractionAdaptation() {
      wx.showLoading({ title: '获取交互适配...' });
      
      wx.request({
        url: `${this.data.apiBaseUrl}/interaction/adaptation/`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        success: (res) => {
          wx.hideLoading();
          
          if (res.data.status === 'success') {
            const interactionAdaptation = res.data.interaction_adaptation;
            
            // 更新本地交互配置
            wx.setStorageSync('interaction_adaptation', interactionAdaptation);
            
            // 根据适配策略调整交互界面和行为
            this.applyInteractionAdaptation(interactionAdaptation);
            
            wx.showToast({
              title: '交互适配成功',
              icon: 'success',
              duration: 2000
            });
          } else {
            wx.showToast({
              title: '获取交互适配失败',
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none',
            duration: 2000
          });
          console.error('交互适配获取失败', err);
        }
      });
    },
    
    // 应用交互适配策略
    applyInteractionAdaptation(adaptation) {
      // 根据推荐的交互风格调整界面
      switch (adaptation.recommended_interaction_style) {
        case '语音导向':
          this.setData({
            voiceInputEnabled: true,
            buttonInputEnabled: false
          });
          break;
        case '操作导向':
          this.setData({
            voiceInputEnabled: false,
            buttonInputEnabled: true
          });
          break;
        default:
          this.setData({
            voiceInputEnabled: true,
            buttonInputEnabled: true
          });
      }
      
      // 根据沟通语气调整界面文案和交互语气
      this.setData({
        communicationTone: adaptation.communication_tone
      });
      
      // 根据个性化程度调整推荐和交互细节
      const personalizationLevel = adaptation.personalization_level;
      this.setData({
        personalizationWeight: personalizationLevel
      });
    },
    
    // 手动训练交互智能模型
    trainInteractionModel() {
      wx.showLoading({ title: '训练交互模型...' });
      
      wx.request({
        url: `${this.data.apiBaseUrl}/interaction/model/train/`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        success: (res) => {
          wx.hideLoading();
          
          if (res.data.status === 'success') {
            wx.showToast({
              title: '模型训练成功',
              icon: 'success',
              duration: 2000
            });
          } else {
            wx.showToast({
              title: '模型训练失败',
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '网络错误，请重试',
            icon: 'none',
            duration: 2000
          });
          console.error('交互模型训练失败', err);
        }
      });
    },
    
    // 记录网络错误日志
    recordNetworkErrorLog(errorData) {
        try {
            // 获取现有的错误日志
            const errorLogs = wx.getStorageSync('networkErrorLogs') || [];
            
            // 添加新的错误日志
            const newErrorLog = {
                timestamp: Date.now(),
                ...errorData
            };
            
            errorLogs.push(newErrorLog);
            
            // 限制错误日志数量，防止存储过多
            const MAX_ERROR_LOGS = 50;
            const trimmedErrorLogs = errorLogs.slice(-MAX_ERROR_LOGS);
            
            // 保存到本地存储
            wx.setStorageSync('networkErrorLogs', trimmedErrorLogs);

            // 可选：上传错误日志
            this.uploadNetworkErrorLogs();
        } catch (error) {
            console.error('记录网络错误日志失败', error);
        }
    },
    
    // 图片加载错误处理
    onImageError(e) {
        console.error('Image load error:', e);
        const errorType = e.currentTarget.dataset.type;
        
        // 详细的错误日志
        console.log('Error Details:', {
            errorType: errorType,
            timestamp: new Date().toISOString(),
            userAgent: wx.getSystemInfoSync()
        });
        
        // 根据错误类型设置默认头像
        if (errorType === 'user') {
            this.setData({
                'userInfo.avatarUrl': '/images/profile_icon.png'
            });
        } else if (errorType === 'ai') {
            this.setData({
                'aiAssistant.avatar': '/images/ai-avatar.png'
            });
        }
        
        // 尝试重新获取用户信息
        wx.getUserProfile({
            desc: '获取头像',
            success: (res) => {
                if (errorType === 'user') {
                    this.setData({
                        'userInfo.avatarUrl': res.userInfo.avatarUrl
                    });
                }
            },
            fail: () => {
                wx.showToast({
                    title: '获取头像失败',
                    icon: 'none'
                });
            }
        });
    },

    // 增加图片预加载机制
    preloadAvatars() {
        const avatars = [
            '/images/profile_icon.png', 
            '/images/ai-avatar.png'
        ];
        
        avatars.forEach(avatarPath => {
            wx.downloadFile({
                url: avatarPath,
                success: (res) => {
                    if (res.statusCode === 200) {
                        console.log(`Preloaded avatar: ${avatarPath}`);
                    }
                },
                fail: (err) => {
                    console.error(`Failed to preload avatar: ${avatarPath}`, err);
                }
            });
        });
    },
});
