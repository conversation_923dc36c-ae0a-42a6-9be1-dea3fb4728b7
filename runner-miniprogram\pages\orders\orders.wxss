page {
    background-color: #f5f5f5;
}

.orders-container {
    padding: 20rpx;
}

.orders-tabs {
    display: flex;
    justify-content: space-around;
    background-color: white;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
}

.orders-tab {
    flex: 1;
    text-align: center;
    padding: 20rpx;
    color: #666;
}

.orders-tab.active {
    color: #07c160;
    border-bottom: 4rpx solid #07c160;
}

.orders-list {
    height: calc(100vh - 100rpx);
}

.order-item {
    background-color: white;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;
}

.order-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10rpx;
}

.order-id {
    font-size: 28rpx;
    color: #666;
}

.order-status {
    font-size: 28rpx;
}

.order-status.pending {
    color: #1989fa;
}

.order-status.completed {
    color: #07c160;
}

.order-status.cancelled {
    color: #ff4d4f;
}

.order-content {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
}

.order-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 10rpx;
    margin-right: 20rpx;
}

.order-details {
    display: flex;
    flex-direction: column;
}

.order-title {
    font-size: 32rpx;
    font-weight: bold;
}

.order-description {
    font-size: 28rpx;
    color: #666;
}

.order-footer {
    display: flex;
    justify-content: space-between;
}

.order-price {
    font-size: 36rpx;
    color: #07c160;
    font-weight: bold;
}

.order-time {
    font-size: 28rpx;
    color: #666;
}
