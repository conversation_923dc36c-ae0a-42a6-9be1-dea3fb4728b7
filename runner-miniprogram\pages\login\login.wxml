<view class="container">
    <canvas 
        type="2d" 
        id="starryCanvas" 
        class="starry-background"
        disable-scroll="true"
    ></canvas>
    
    <view class="login-section">
        <view class="index-logo-container">
            <view class="index-rocket-wrapper">
                <text class="index-logo-text">🚀</text>
                <view class="index-rocket-flame"></view>
            </view>
        </view>
        
        <view class="welcome-text">
            <text class="title">顺道跑腿</text>
            <text class="subtitle">您的生活助手</text>
        </view>
        
        <view class="login-actions">
            <button 
                class="wechat-login-btn" 
                bindtap="handleWechatLogin"
                loading="{{loginLoading}}"
            >
                微信一键登录
            </button>
            
            <button 
                class="debug-login-btn" 
                bindtap="handleDebugLogin"
            >
                调试登录
            </button>
            
            <view class="privacy-agreement">
                <view class="privacy-checkbox" bindtap="togglePrivacyAgreement">
                    <view class="checkbox {{privacyAgreed ? 'checked' : ''}}"></view>
                </view>
                <text class="privacy-text">我已阅读并同意</text>
                <text 
                    class="privacy-link" 
                    bindtap="showPrivacyPolicy"
                >《用户隐私协议》</text>
            </view>
        </view>
    </view>
</view>
