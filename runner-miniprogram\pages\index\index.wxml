<view class="container">
    <view class="chat-header">
        <view class="header-left">
            <view class="notice-marquee" wx:if="{{notices && notices.length}}">
                <image class="notice-icon" src="/images/notice_icon.png"></image>
                <swiper 
                    class="notice-swiper" 
                    vertical="{{true}}" 
                    autoplay="{{true}}" 
                    interval="3000" 
                    circular="{{true}}"
                >
                    <block wx:for="{{notices}}" wx:key="index">
                        <swiper-item class="notice-item">
                            <text class="notice-text">{{item}}</text>
                        </swiper-item>
                    </block>
                </swiper>
            </view>
        </view>
        <view class="header-title">{{aiAssistant.name || 'AI助手'}}</view>
        <view class="header-right">
            <image 
                class="header-more" 
                src="/images/more_icon.png" 
                bindtap="onMoreTap"
            ></image>
        </view>
    </view>

    <scroll-view 
        scroll-y 
        class="chat-messages"
        scroll-with-animation
        scroll-into-view="msg-{{messages[messages.length-1].id}}"
    >
        <block wx:for="{{messages}}" wx:key="id">
            <view 
                id="msg-{{item.id}}"
                class="message-item {{item.type === 'user' ? 'user-message-container' : 'ai-message-container'}}"
            >
                <!-- 系统消息 -->
                <view 
                    wx:if="{{item.type === 'system'}}" 
                    class="system-message"
                >
                    {{item.content}}
                </view>

                <!-- 用户消息 -->
                <view 
                    wx:elif="{{item.type === 'user'}}" 
                    class="message-wrapper user-message-wrapper"
                >
                    <view class="message-content user-message-content">
                        {{item.content}}
                        <text 
                            wx:if="{{item.status === 'error'}}" 
                            class="message-status error"
                        >
                            发送失败
                        </text>
                    </view>
                    <image 
                        src="{{userInfo.avatarUrl || '/images/profile_icon.png'}}" 
                        class="avatar user-avatar"
                        mode="aspectFill"
                        lazy-load="true"
                        show-menu-by-longpress="true"
                        binderror="onImageError"
                        data-type="user"
                    />
                </view>

                <!-- AI消息 -->
                <view 
                    wx:elif="{{item.type === 'ai'}}" 
                    class="message-wrapper ai-message-wrapper"
                >
                    <image 
                        src="{{aiAssistant.avatar || '/images/ai-avatar.png'}}" 
                        class="avatar ai-avatar"
                        mode="aspectFill"
                        lazy-load="true"
                        show-menu-by-longpress="true"
                        binderror="onImageError"
                        data-type="ai"
                    />
                    <view class="message-content ai-message-content">
                        {{item.content}}
                        <view 
                            wx:if="{{item.extra.confidence}}" 
                            class="confidence-tag"
                        >
                            置信度: {{item.extra.confidence}}
                        </view>
                    </view>
                </view>
            </view>
        </block>
    </scroll-view>

    <view class="input-container">
        <view class="input-tools">
            <image 
                class="input-icon voice-toggle" 
                src="{{inputMode === 'voice' ? '/images/keyboard_icon.png' : '/images/voice_icon.png'}}"
                bindtap="toggleInputMode"
            ></image>

            <view class="input-area">
                <block wx:if="{{inputMode === 'voice'}}">
                    <view 
                        class="chat-voice-input-btn {{isRecording ? 'recording' : ''}}" 
                        bindtouchstart="startVoiceRecord" 
                        bindtouchend="stopVoiceRecord"
                        bindtouchcancel="cancelVoiceRecord"
                    >
                        <text>{{isRecording ? '松开 结束' : '按住 说话'}}</text>
                    </view>
                </block>
                <block wx:else>
                    <view class="text-input-container">
                        <input 
                            class="chat-input" 
                            type="text" 
                            placeholder="{{inputPlaceholder}}" 
                            value="{{inputMessage}}"
                            bindinput="onInputChange"
                            bindconfirm="sendMessage"
                            focus="{{inputFocused}}"
                            confirm-type="send"
                            cursor-spacing="10"
                        ></input>
                        <view 
                            wx:if="{{inputMessage}}" 
                            class="send-btn" 
                            bindtap="sendMessage"
                        >发送</view>
                    </view>
                </block>
            </view>

            <image 
                class="input-icon more-actions" 
                src="/images/plus_icon.png"
                bindtap="showMoreActions"
            ></image>
        </view>
    </view>

    <view wx:if="{{showMoreActionsPanel}}" class="more-actions-panel">
        <view class="more-actions-grid">
            <view class="more-action-item" bindtap="takePhoto">
                <view class="more-action-icon">
                    <image 
                        class="icon-image" 
                        src="/images/camera_icon.png"
                    ></image>
                </view>
                <text class="action-text">拍照</text>
            </view>
            <view class="more-action-item" bindtap="chooseFromAlbum">
                <view class="more-action-icon">
                    <image 
                        class="icon-image" 
                        src="/images/album_icon.png"
                    ></image>
                </view>
                <text class="action-text">相册</text>
            </view>
            <view class="more-action-item" bindtap="shareLocation">
                <view class="more-action-icon">
                    <image 
                        class="icon-image" 
                        src="/images/location_icon.png"
                    ></image>
                </view>
                <text class="action-text">位置</text>
            </view>
            <view class="more-action-item" bindtap="goToProfile">
                <view class="more-action-icon">
                    <image 
                        class="icon-image" 
                        src="/images/profile_icon.png"
                    ></image>
                </view>
                <text class="action-text">我的</text>
            </view>
        </view>
    </view>
</view>
