.privacy-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: #f5f5f5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.privacy-header {
    background-color: #07c160;
    color: white;
    padding: 40rpx 30rpx;
    text-align: center;
}

.privacy-title {
    font-size: 48rpx;
    font-weight: bold;
    display: block;
}

.privacy-subtitle {
    font-size: 28rpx;
    opacity: 0.8;
    margin-top: 10rpx;
}

.privacy-content {
    flex-grow: 1;
    padding: 30rpx;
}

.privacy-section {
    background-color: white;
    border-radius: 15rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 10rpx rgba(0,0,0,0.05);
}

.privacy-section-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #07c160;
    margin-bottom: 20rpx;
    display: block;
}

.privacy-section-text {
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
    white-space: pre-wrap;
}

.privacy-footer {
    background-color: #f5f5f5;
    color: #999;
    text-align: center;
    padding: 20rpx;
    font-size: 24rpx;
}

.privacy-footer-text {
    display: block;
    margin: 10rpx 0;
}
