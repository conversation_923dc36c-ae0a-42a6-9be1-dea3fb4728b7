// security_module.js
const securityService = require('./security_service.js');

const SecurityModule = {
    initializeSecurity(page) {
        try {
            // 生成安全令牌
            const userContext = this.getUserContext();
            const userId = page.data.userId || 'test_user';

            securityService.generateSecurityToken(userId, userContext)
                .then(securityToken => {
                    // 更新安全状态
                    page.setData({
                        securityStatus: {
                            riskLevel: 'low',
                            anomalyScore: 0,
                            securityToken: securityToken
                        }
                    });

                    // 异常行为检测
                    this.detectAnomalousBehavior(page);
                })
                .catch(error => {
                    console.error('安全初始化失败:', error);
                    wx.showToast({
                        title: '安全检查异常',
                        icon: 'none'
                    });
                });
        } catch (error) {
            console.error('安全初始化异常:', error);
            wx.showToast({
                title: '安全检查异常',
                icon: 'none'
            });
        }
    },

    getUserContext() {
        return {
            timestamp: new Date().toISOString(),
            deviceInfo: wx.getSystemInfoSync(),
            networkType: wx.getNetworkType()
        };
    },

    detectAnomalousBehavior(page) {
        try {
            const userBehavior = this.collectUserBehavior(page);
            
            securityService.detectAnomalousBehavior(userBehavior)
                .then(anomalyResult => {
                    // 更新安全状态
                    page.setData({
                        'securityStatus.riskLevel': anomalyResult.anomalyLevel,
                        'securityStatus.anomalyScore': anomalyResult.anomalyScore
                    });

                    // 根据风险等级采取行动
                    this.handleSecurityRisk(page, anomalyResult);
                })
                .catch(error => {
                    console.error('异常行为检测失败:', error);
                });
        } catch (error) {
            console.error('异常行为检测异常:', error);
        }
    },

    collectUserBehavior(page) {
        return {
            interactionFrequency: this.calculateInteractionFrequency(page),
            timeBetweenInteractions: this.calculateTimeBetweenInteractions(page),
            locationDiversity: this.calculateLocationDiversity(page),
            deviceChangeFrequency: this.calculateDeviceChangeFrequency(page),
            transactionAmountVariation: this.calculateTransactionAmountVariation(page),
            navigationPatternDeviation: this.calculateNavigationPatternDeviation(page)
        };
    },

    handleSecurityRisk(page, anomalyResult) {
        switch (anomalyResult.anomalyLevel) {
            case 'low':
                this.logSecurityEvent(page, 'low_risk', anomalyResult);
                break;
            case 'medium':
                wx.showModal({
                    title: '安全提醒',
                    content: '检测到异常行为，请注意账户安全',
                    confirmText: '我知道了',
                    showCancel: false
                });
                this.logSecurityEvent(page, 'medium_risk', anomalyResult);
                break;
            case 'high':
                wx.showModal({
                    title: '安全警告',
                    content: '检测到高风险异常行为，建议立即采取安全措施',
                    confirmText: '联系客服',
                    cancelText: '稍后处理',
                    success: (res) => {
                        if (res.confirm) {
                            this.contactCustomerService(page);
                        }
                    }
                });
                this.logSecurityEvent(page, 'high_risk', anomalyResult);
                break;
        }
    },

    contactCustomerService(page) {
        wx.navigateTo({
            url: '/pages/customer_service/index'
        });
    },

    logSecurityEvent(page, level, eventData) {
        wx.getStorage({
            key: 'securityEventLog',
            success: (res) => {
                const logs = res.data || [];
                logs.push({
                    timestamp: new Date().toISOString(),
                    level: level,
                    details: eventData
                });
                wx.setStorage({
                    key: 'securityEventLog',
                    data: logs
                });
            },
            fail: () => {
                wx.setStorage({
                    key: 'securityEventLog',
                    data: [{
                        timestamp: new Date().toISOString(),
                        level: level,
                        details: eventData
                    }]
                });
            }
        });
    },

    // 辅助计算方法（需要根据实际业务逻辑实现）
    calculateInteractionFrequency(page) { return 1; },
    calculateTimeBetweenInteractions(page) { return 30; },
    calculateLocationDiversity(page) { return 2; },
    calculateDeviceChangeFrequency(page) { return 1; },
    calculateTransactionAmountVariation(page) { return 0.2; },
    calculateNavigationPatternDeviation(page) { return 0.1; }
};

module.exports = SecurityModule;
