Page({
    data: {
        activeTab: 0,
        coupons: [
            {
                amount: 10,
                condition: 50,
                title: '跑腿服务优惠券',
                startTime: '2023-12-01',
                endTime: '2023-12-31',
                status: 'available'
            },
            {
                amount: 20,
                condition: 100,
                title: '新用户专享优惠券',
                startTime: '2023-11-15',
                endTime: '2023-12-15',
                status: 'used'
            },
            {
                amount: 5,
                condition: 30,
                title: '首次下单优惠券',
                startTime: '2023-10-01',
                endTime: '2023-11-30',
                status: 'expired'
            }
        ]
    },

    onLoad: function() {
        // 可以在这里加载真实的优惠券数据
    },

    switchTab: function(e) {
        const tab = e.currentTarget.dataset.tab;
        this.setData({
            activeTab: parseInt(tab)
        });
    }
});
