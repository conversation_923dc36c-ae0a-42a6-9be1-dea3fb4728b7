<view class="orders-container">
    <view class="orders-tabs">
        <view class="orders-tab {{activeTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">
            全部订单
        </view>
        <view class="orders-tab {{activeTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">
            进行中
        </view>
        <view class="orders-tab {{activeTab === 2 ? 'active' : ''}}" bindtap="switchTab" data-tab="2">
            已完成
        </view>
    </view>

    <scroll-view scroll-y class="orders-list">
        <block wx:for="{{orders}}" wx:key="index">
            <view class="order-item" bindtap="viewOrderDetail" data-id="{{item.id}}">
                <view class="order-header">
                    <text class="order-id">订单号: {{item.id}}</text>
                    <text class="order-status {{item.status}}">{{item.statusText}}</text>
                </view>
                <view class="order-content">
                    <text class="order-image">{{item.imageEmoji}}</text>
                    <view class="order-details">
                        <text class="order-title">{{item.title}}</text>
                        <text class="order-description">{{item.description}}</text>
                    </view>
                </view>
                <view class="order-footer">
                    <text class="order-price">¥{{item.price}}</text>
                    <text class="order-time">{{item.time}}</text>
                </view>
            </view>
        </block>
    </scroll-view>
</view>
