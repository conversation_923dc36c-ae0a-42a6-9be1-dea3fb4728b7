App({
    onLaunch() {
        // 检查登录状态
        this.checkLoginStatus();
    },

    checkLoginStatus() {
        const token = wx.getStorageSync('token');
        const userInfo = wx.getStorageSync('userInfo');
        
        if (token && userInfo) {
            this.globalData.token = token;
            this.globalData.userInfo = userInfo;
        }
    },

    // 模拟登录方法
    mockLogin(nickName = '测试用户') {
        return new Promise((resolve, reject) => {
            // 模拟用户信息
            const mockUserInfo = {
                nickName: nickName,
                avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/mock_avatar.jpg',
                gender: 1,
                country: '中国',
                province: '广东',
                city: '深圳'
            };

            // 模拟生成token
            const token = this.generateMockToken();

            // 保存用户信息和token
            wx.setStorageSync('token', token);
            wx.setStorageSync('userInfo', mockUserInfo);
            
            this.globalData.token = token;
            this.globalData.userInfo = mockUserInfo;
            
            resolve(mockUserInfo);
        });
    },

    // 生成模拟token
    generateMockToken() {
        const timestamp = new Date().getTime();
        return `mock_token_${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
    },

    wxLogin() {
        // 对于模拟环境，直接调用mockLogin
        return this.mockLogin();
    },

    logout() {
        // 清除本地存储
        wx.removeStorageSync('token');
        wx.removeStorageSync('userInfo');
        
        // 清除全局数据
        this.globalData.token = null;
        this.globalData.userInfo = null;
        
        // 返回登录页
        wx.reLaunch({
            url: '/pages/index/index'
        });
    },

    globalData: {
        userInfo: null,
        token: null
    }
});
