const personalityConfig = {
    defaultPersonality: {
        friendly: 0.8,
        professional: 0.7,
        humorous: 0.6
    },
    personalityAdjustments: {
        '友好': { friendly: 0.1 },
        '专业': { professional: 0.1 },
        '幽默': { humorous: 0.1 }
    }
};

function adjustPersonality(currentPersonality, interaction) {
    const adjustment = personalityConfig.personalityAdjustments[interaction] || {};
    
    return Object.fromEntries(
        Object.entries(currentPersonality).map(([key, value]) => 
            [key, Math.min(1, value + (adjustment[key] || 0))]
        )
    );
}

function selectResponseStyle(personality) {
    if (personality.professional > 0.7) return 'professional';
    if (personality.humorous > 0.7) return 'humorous';
    return 'default';
}

module.exports = {
    personalityConfig,
    adjustPersonality,
    selectResponseStyle
};
