.container {
    position: relative;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(180deg, #0B1026 0%, #1A2980 100%);
    overflow: hidden;
}

.starry-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.login-section {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 80%;
    max-width: 400px;
}

.index-logo-container {
    margin-bottom: 40rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.index-rocket-wrapper {
    display: inline-block;
    animation: index-rocket-fly 2s ease-in-out infinite alternate;
    transform-origin: bottom center;
    transform: rotate(45deg);
    z-index: 1;
    margin-left: -20rpx;
}

.index-logo-text {
    font-size: 120rpx;
    display: inline-block;
    transform: rotate(-45deg);
    z-index: 2;
}

.index-rocket-flame {
    position: absolute;
    bottom: -10rpx;
    left: 50%;
    transform: translateX(-50%) rotate(180deg);
    width: 30rpx;
    height: 80rpx;
    background: linear-gradient(
        to bottom, 
        rgba(255,106,0,0.8), 
        rgba(255,69,0,0.6), 
        transparent
    );
    border-radius: 50% 50% 0 0;
    animation: index-flame-animation 0.3s infinite alternate;
    z-index: 0;
}

@keyframes index-rocket-fly {
    0% {
        transform: translate(-10rpx, 0) rotate(45deg);
    }
    100% {
        transform: translate(10rpx, -20rpx) rotate(45deg);
    }
}

@keyframes index-flame-animation {
    0% {
        height: 80rpx;
        opacity: 0.7;
        transform: translateX(-50%) rotate(180deg) scaleX(0.9);
    }
    100% {
        height: 100rpx;
        opacity: 1;
        transform: translateX(-50%) rotate(180deg) scaleX(1.1);
    }
}

.welcome-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 80rpx;
    color: white;
    text-align: center;
}

.title {
    font-size: 72rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    letter-spacing: 10rpx;
}

.subtitle {
    font-size: 32rpx;
    color: rgba(255,255,255,0.7);
    letter-spacing: 4rpx;
}

.login-actions {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.wechat-login-btn {
    width: 100%;
    background-color: #07C160;
    color: white;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.debug-login-btn {
    width: 100%;
    background-color: #4A8FFF;
    color: white;
    margin-bottom: 20rpx;
}

.privacy-agreement {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20rpx;
    font-size: 24rpx;
    color: rgba(255,255,255,0.7);
}

.privacy-checkbox {
    width: 24rpx;
    height: 24rpx;
    border: 2rpx solid white;
    margin-right: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.privacy-checkbox .checkbox {
    width: 16rpx;
    height: 16rpx;
    background-color: transparent;
    transition: background-color 0.3s;
}

.privacy-checkbox .checkbox.checked {
    background-color: #07C160;
}

.privacy-text {
    margin-left: 10rpx;
}

.privacy-link {
    color: #4A8FFF;
    text-decoration: underline;
}
