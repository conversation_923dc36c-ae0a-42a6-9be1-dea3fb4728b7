<view class="container runner-page">
    <view class="header">
        <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
        <text class="username">{{userInfo.nickName}}</text>
    </view>

    <view class="tasks-section">
        <text class="section-title">当前任务</text>
        <block wx:for="{{currentTasks}}" wx:key="id">
            <view class="task-card">
                <view class="task-info">
                    <text class="task-type">{{item.type}}</text>
                    <text class="task-address">{{item.address}}</text>
                    <text class="task-reward">¥{{item.reward}}</text>
                </view>
                <button 
                    class="accept-btn" 
                    data-task-id="{{item.id}}"
                    bindtap="acceptTask"
                >
                    接单
                </button>
            </view>
        </block>
    </view>

    <view class="performance-section">
        <text class="section-title">我的业绩</text>
        <view class="performance-stats">
            <view class="stat-item">
                <text class="stat-label">本月接单</text>
                <text class="stat-value">{{userInfo.monthlyOrders}}单</text>
            </view>
            <view class="stat-item">
                <text class="stat-label">完成率</text>
                <text class="stat-value">{{userInfo.completionRate}}%</text>
            </view>
        </view>
    </view>
</view>
