// 骑手首页逻辑
Page({
    data: {
        currentTasks: [],
        userInfo: null,
        location: null
    },

    onLoad() {
        this.initRunnerPage();
    },

    initRunnerPage() {
        this.checkRunnerLogin();
        this.getCurrentLocation();
        this.fetchCurrentTasks();
    },

    checkRunnerLogin() {
        wx.login({
            success: (res) => {
                if (res.code) {
                    this.performRunnerLogin(res.code);
                }
            }
        });
    },

    performRunnerLogin(code) {
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/runner/login`,
            method: 'POST',
            data: { code: code },
            success: (loginRes) => {
                wx.setStorageSync('runner_token', loginRes.data.token);
                this.loadRunnerProfile();
            }
        });
    },

    loadRunnerProfile() {
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/runner/profile`,
            method: 'GET',
            header: { 'Authorization': wx.getStorageSync('runner_token') },
            success: (profileRes) => {
                this.setData({
                    userInfo: profileRes.data.profile
                });
            }
        });
    },

    getCurrentLocation() {
        wx.getLocation({
            type: 'gcj02',
            success: (res) => {
                this.setData({
                    location: {
                        latitude: res.latitude,
                        longitude: res.longitude
                    }
                });
                this.uploadRunnerLocation(res);
            }
        });
    },

    uploadRunnerLocation(location) {
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/runner/location`,
            method: 'POST',
            data: location,
            header: { 'Authorization': wx.getStorageSync('runner_token') }
        });
    },

    fetchCurrentTasks() {
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/runner/tasks`,
            method: 'GET',
            header: { 'Authorization': wx.getStorageSync('runner_token') },
            success: (tasksRes) => {
                this.setData({
                    currentTasks: tasksRes.data.tasks
                });
            }
        });
    },

    acceptTask(e) {
        const taskId = e.currentTarget.dataset.taskId;
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/runner/accept_task`,
            method: 'POST',
            data: { task_id: taskId },
            header: { 'Authorization': wx.getStorageSync('runner_token') },
            success: () => {
                wx.showToast({ title: '接单成功' });
                this.fetchCurrentTasks();
            }
        });
    }
});
