/* 全局样式 */
.customer-profile-container {
  background-color: #f4f4f4;
  min-height: 100vh;
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0 30rpx;
}

/* 用户头部 */
.customer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.customer-header-actions {
  display: flex;
  align-items: center;
}

.customer-settings-icon {
  font-size: 48rpx;
  color: #3498db;
  cursor: pointer;
}

.customer-avatar-container {
  display: flex;
  align-items: center;
}

.customer-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.customer-avatar-default {
  background-color: #3498db;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.customer-avatar-text {
  color: white;
  font-size: 60rpx;
}

.customer-user-info {
  display: flex;
  flex-direction: column;
}

.customer-username {
  font-size: 36rpx;
  font-weight: bold;
}

.customer-user-desc {
  font-size: 28rpx;
  color: #7f8c8d;
  margin-top: 10rpx;
}

/* 快速服务 */
.customer-service-menu {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.customer-service-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.customer-service-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.customer-service-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #f1f2f6;
  border-radius: 15rpx;
}

.customer-service-icon {
  font-size: 60rpx;
  margin-bottom: 10rpx;
}

.customer-service-label {
  font-size: 28rpx;
  color: #2c3e50;
}

/* 订单统计 */
.customer-order-stats {
  display: flex;
  justify-content: space-between;
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.customer-stat-item {
  text-align: center;
  flex: 1;
}

.customer-stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #3498db;
}

.customer-stat-label {
  display: block;
  font-size: 28rpx;
  color: #7f8c8d;
}

/* 常用功能 */
.customer-common-menu {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.customer-common-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.customer-common-list {
  display: flex;
  flex-direction: column;
}

.customer-common-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
}

.customer-common-item:last-child {
  border-bottom: none;
}

.customer-common-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  color: #3498db;
}

.customer-common-label {
  flex-grow: 1;
  font-size: 32rpx;
  color: #2c3e50;
}

.customer-common-desc {
  font-size: 28rpx;
  color: #7f8c8d;
}

/* 个人设置 */
.customer-settings-menu {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.customer-settings-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.customer-settings-list {
  display: flex;
  flex-direction: column;
}

.customer-settings-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
}

.customer-settings-item:last-child {
  border-bottom: none;
}

.customer-settings-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
  color: #3498db;
}

.customer-settings-label {
  font-size: 32rpx;
  color: #2c3e50;
}

/* 移除单独的退出登录样式 */
.customer-logout-container,
.customer-logout-btn,
.customer-logout-icon,
.customer-logout-text {
  display: none;
}

/* 页面整体背景 */
.customer-page {
  background-color: #f5f7f9;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.profile-container {
    min-height: 100vh;
    background: #050b1f;
    padding: 30rpx;
    position: relative;
    overflow: hidden;
}

.profile-dynamic-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    background-color: #050b1f;  /* 添加明确的背景色 */
}

.profile-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #1a1b4b 0%, #050b1f 100%);
    opacity: 0.8;
}

.profile-particle {
    position: absolute;
    width: 4rpx;
    height: 4rpx;
    background: #ffffff;
    border-radius: 50%;
    animation: profile-float 20s infinite linear;
}

@keyframes profile-float {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
}

.profile-glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 30rpx;
    padding: 40rpx;
    margin-bottom: 30rpx;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.profile-avatar-container {
    position: relative;
    margin-bottom: 20rpx;
}

.profile-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 80rpx;
  color: white;
}

.profile-status-badge {
    position: absolute;
    bottom: 10rpx;
    right: 10rpx;
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    background: #ff4757;
    border: 4rpx solid #050b1f;
}

.profile-status-badge-online {
    background: #2ed573;
}

.profile-quick-actions {
    display: flex;
    justify-content: space-between;
    margin: 40rpx 0;
}

.profile-action-card {
    width: 200rpx;
    height: 200rpx;
    perspective: 1000rpx;
}

.profile-card-inner {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transform-style: preserve-3d;
    transition: transform 0.5s;
}

.profile-action-card:active .profile-card-inner {
    transform: rotateY(180deg);
}

.profile-order-cards {
    white-space: nowrap;
    margin: 40rpx -30rpx;
    padding: 0 30rpx;
}

.profile-order-card {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    padding: 30rpx;
    margin-right: 20rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20rpx;
    min-width: 160rpx;
}

.profile-feature-menu {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30rpx;
    margin: 40rpx 0;
}

.profile-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.profile-menu-icon-wrapper {
    position: relative;
    width: 80rpx;
    height: 80rpx;
    margin-bottom: 10rpx;
}

.profile-menu-icon {
  font-size: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255,255,255,0.1);
}

.profile-ripple-effect {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: scale(0);
    transition: transform 0.3s;
}

.profile-menu-item:active .profile-ripple-effect {
    transform: scale(1.5);
    opacity: 0;
}

.profile-activity-card {
    position: relative;
    border-radius: 20rpx;
    overflow: hidden;
    margin-top: 40rpx;
}

.profile-activity-placeholder {
    width: 100%;
    height: 200rpx;
    background: linear-gradient(45deg, #1a1b4b, #050b1f);
    border-radius: 20rpx;
}

.profile-activity-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    color: #fff;
}

.profile-activity-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
}

.profile-activity-desc {
    display: block;
    font-size: 24rpx;
    margin-top: 10rpx;
}

/* 任务列表样式 */
.task-container {
  background: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 20rpx;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.task-title {
  font-size: 32rpx;
  font-weight: bold;
}

.task-add-btn {
  background: #07c160;
  color: #fff;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.task-list {
  padding-top: 20rpx;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.task-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.task-delete {
  color: #ff4d4f;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}

.no-tasks {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx 0;
}
