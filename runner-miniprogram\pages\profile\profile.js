const app = getApp();
const api = require('../../utils/api.js');

Page({
  data: {
    userInfo: {
      nickName: '',
      avatarUrl: '',
      phone: '',
      balance: '0.00'
    },
    orderStats: {
      pending: 0,
      processing: 0,
      completed: 0
    },
    couponCount: 0,
    tasks: []  // 新增任务列表
  },

  onLoad(options) {
    this.getUserInfo();
    this.getOrderStats();
    this.getCouponCount();
    this.loadTasks();  // 加载任务列表
  },

  // 加载任务列表
  async loadTasks() {
    try {
      const tasks = await api.getTasks();
      this.setData({ tasks });
    } catch (error) {
      wx.showToast({
        title: '加载任务失败',
        icon: 'none'
      });
    }
  },

  // 添加任务
  async addTask() {
    const that = this;
    wx.showModal({
      title: '添加任务',
      editable: true,
      placeholderText: '请输入任务内容',
      success: async (res) => {
        if (res.confirm && res.content) {
          try {
            await api.addTask(res.content);
            await that.loadTasks();
            wx.showToast({
              title: '添加成功',
              icon: 'success'
            });
          } catch (error) {
            wx.showToast({
              title: '添加失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 删除任务
  async deleteTask(e) {
    const taskId = e.currentTarget.dataset.id;
    try {
      await api.deleteTask(taskId);
      await this.loadTasks();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
    } catch (error) {
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  getUserInfo() {
    // 模拟获取用户信息
    const mockUserInfo = {
      nickName: '小明',
      avatarUrl: '',
      phone: '186****1234',
      balance: '128.50'
    };
    this.setData({ userInfo: mockUserInfo });
  },

  getOrderStats() {
    // 模拟获取订单统计
    const mockOrderStats = {
      pending: 2,
      processing: 1,
      completed: 15
    };
    this.setData({ orderStats: mockOrderStats });
  },

  getCouponCount() {
    // 模拟获取优惠券数量
    this.setData({ couponCount: 3 });
  },

  // 快速服务
  goToCreateOrder() {
    wx.navigateTo({ url: '/pages/order/create' });
  },

  goToOrderList(e) {
    const type = e.currentTarget.dataset.type;
    wx.navigateTo({ url: `/pages/order/list?type=${type}` });
  },

  goToAddressManage() {
    wx.navigateTo({ url: '/pages/address/list' });
  },

  goToCustomerService() {
    wx.navigateTo({ url: '/pages/service/index' });
  },

  // 常用功能
  goToWallet() {
    wx.navigateTo({ url: '/pages/wallet/index' });
  },

  goToCoupon() {
    wx.navigateTo({ url: '/pages/coupon/list' });
  },

  goToInvite() {
    wx.navigateTo({ url: '/pages/invite/index' });
  },

  // 个人设置
  goToPersonalInfo() {
    wx.navigateTo({ url: '/pages/profile/edit' });
  },

  goToSettings() {
    wx.navigateTo({ url: '/pages/settings/index' });
  },

  goToIntelligentService() {
    wx.navigateTo({
      url: '/pages/intelligent-service/index'
    });
  },

  logout() {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的用户信息
          wx.clearStorage({
            success: () => {
              // 跳转到登录页
              wx.reLaunch({
                url: '/pages/login/index'
              });
            }
          });
        }
      }
    });
  }
});
