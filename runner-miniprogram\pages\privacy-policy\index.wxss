page {
    background-color: #0B1026;
    color: #ffffff;
}

.privacy-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 40rpx;
}

.privacy-header {
    text-align: center;
    margin-bottom: 40rpx;
}

.privacy-title {
    display: block;
    font-size: 52rpx;
    font-weight: bold;
    color: #2575fc;
    margin-bottom: 10rpx;
}

.privacy-subtitle {
    display: block;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.6);
}

.privacy-content {
    flex-grow: 1;
    margin-bottom: 40rpx;
}

.privacy-section {
    margin-bottom: 40rpx;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 16rpx;
    padding: 30rpx;
    transition: background-color 0.3s;
}

.privacy-section:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.section-title {
    display: block;
    font-size: 36rpx;
    color: #2575fc;
    margin-bottom: 20rpx;
    border-bottom: 2rpx solid rgba(37, 117, 252, 0.3);
    padding-bottom: 10rpx;
}

.section-text {
    font-size: 28rpx;
    line-height: 1.8;
    color: rgba(255, 255, 255, 0.7);
    white-space: pre-wrap;
}

.privacy-footer {
    text-align: center;
    padding: 20rpx;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 16rpx;
}

.footer-text {
    display: block;
    color: rgba(255, 255, 255, 0.5);
    font-size: 24rpx;
    margin-bottom: 10rpx;
}
