// API 基础地址
const BASE_URL = wx.getAccountInfoSync().miniProgram.envVersion === 'develop' 
  ? 'http://127.0.0.1:8000'  // 使用 127.0.0.1 替代 localhost
  : 'https://your-production-domain.com';  // 生产环境

// 添加环境信息日志
console.log('当前环境:', wx.getAccountInfoSync().miniProgram.envVersion);
console.log('BASE_URL:', BASE_URL);

// 封装请求方法
const request = (url, method = 'GET', data = {}) => {
  return new Promise((resolve, reject) => {
    console.log(`发起请求: ${method} ${BASE_URL}${url}`);
    console.log('请求数据:', data);

    // 获取存储的 token
    const token = wx.getStorageSync('token');
    
    // 准备请求数据
    let requestData = method === 'GET' ? data : JSON.stringify(data);
    
    wx.request({
      url: `${BASE_URL}${url}`,
      method: method,
      data: requestData,
      header: {
        'content-type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: (res) => {
        console.log(`请求成功: ${method} ${url}`, res);
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          console.error('请求失败:', res);
          reject({
            statusCode: res.statusCode,
            message: res.data?.message || '请求失败'
          });
        }
      },
      fail: (err) => {
        console.error(`请求错误: ${method} ${url}`, err);
        // 检查是否是连接被拒绝的错误
        if (err.errMsg && err.errMsg.includes('fail')) {
          reject({
            statusCode: 0,
            message: '无法连接到服务器，请确保服务器已启动'
          });
        } else {
          reject({
            statusCode: 0,
            message: '网络错误，请检查网络连接'
          });
        }
      },
      complete: (res) => {
        console.log(`请求完成: ${method} ${url}`, res);
      }
    });
  });
};

// 导出具体的 API 方法
const api = {
  // 发送消息
  sendMessage: (message) => {
    console.log('发送消息:', message);
    return request('/api/process_message', 'POST', { message: message });
  },

  // 获取地址列表
  getAddresses: () => {
    return request('/api/address/');
  },

  // 添加地址
  addAddress: (addressData) => {
    return request('/api/address/', 'POST', addressData);
  },

  // 获取订单列表
  getOrders: () => {
    return request('/api/order/');
  },

  // 创建订单
  createOrder: (orderData) => {
    return request('/api/order/', 'POST', orderData);
  }
};

module.exports = api;
