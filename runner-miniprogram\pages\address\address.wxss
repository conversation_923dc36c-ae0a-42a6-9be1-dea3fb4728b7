page {
    background-color: #f5f5f5;
}

.address-container {
    padding: 20rpx;
    background-color: #f5f5f5;
}

.address-list {
    background-color: white;
    border-radius: 10rpx;
}

.address-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border-bottom: 1rpx solid #f0f0f0;
}

.address-icon {
    font-size: 48rpx;
    margin-right: 20rpx;
}

.address-details {
    flex-grow: 1;
}

.address-header {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
}

.address-name {
    font-size: 32rpx;
    font-weight: bold;
    margin-right: 20rpx;
}

.address-phone {
    color: #666;
}

.address-default-tag {
    background-color: #07c160;
    color: white;
    font-size: 24rpx;
    padding: 4rpx 8rpx;
    border-radius: 4rpx;
    margin-left: 10rpx;
}

.address-text {
    color: #666;
    font-size: 28rpx;
}

.address-actions {
    display: flex;
    flex-direction: column;
}

.address-edit, 
.address-delete {
    font-size: 40rpx;
    margin: 10rpx 0;
    text-align: center;
}

.address-add-btn {
    margin-top: 20rpx;
    background-color: white;
    text-align: center;
    padding: 20rpx;
    border-radius: 10rpx;
    color: #07c160;
    font-size: 32rpx;
}
