const intentPatterns = {
    '问候': [/你好/, /嗨/, /hi/, /hey/],
    '再见': [/再见/, /拜拜/, /goodbye/],
    '天气': [/天气/, /温度/, /forecast/],
    '时间': [/几点/, /时间/, /现在几点/],
    '帮助': [/帮助/, /怎么用/, /使用说明/],
    '个人信息': [/我是谁/, /我的信息/, /个人信息/],
    '系统信息': [/你是谁/, /你叫什么/, /介绍一下自己/]
};

const predefinedResponses = {
    '问候': [
        "你好！很高兴认识你。",
        "嗨，今天我可以帮你做些什么吗？"
    ],
    '再见': [
        "再见！希望我的服务对你有帮助。",
        "期待下次再与你交谈。"
    ],
    '天气': ["抱歉，天气查询功能暂未开放。"],
    '时间': [wx.getStorageSync('currentTime') || "现在是" + new Date().toLocaleString()],
    '帮助': [
        "我可以帮你进行自然语言对话、信息查询和任务辅助。",
        "有什么我可以帮你的吗？"
    ],
    '个人信息': ["这需要用户登录后才能查看个人信息。"],
    '系统信息': [
        "我是小智，一个智能助手。",
        "我的能力包括自然语言对话、信息查询和任务辅助。"
    ]
};

function recognizeIntent(message) {
    for (let [intent, patterns] of Object.entries(intentPatterns)) {
        for (let pattern of patterns) {
            if (pattern.test(message)) {
                return intent;
            }
        }
    }
    return '普通对话';
}

function generateResponse(intent, message) {
    const responses = predefinedResponses[intent] || [];
    return responses.length > 0 
        ? responses[Math.floor(Math.random() * responses.length)] 
        : null;
}

module.exports = {
    recognizeIntent,
    generateResponse
};
