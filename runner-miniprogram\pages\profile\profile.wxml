<view class="customer-page">
  <view class="customer-profile-container">
    <!-- 用户基本信息 -->
    <view class="customer-header">
      <view class="customer-avatar-container">
        <image class="customer-avatar" src="{{userInfo.avatarUrl}}" wx:if="{{userInfo.avatarUrl}}"/>
        <view class="customer-avatar customer-avatar-default" wx:else>
          <text class="customer-avatar-text">{{userInfo.nickName ? userInfo.nickName[0] : '未'}}</text>
        </view>
        <view class="customer-user-info">
          <text class="customer-username">{{userInfo.nickName || '未登录'}}</text>
          <text class="customer-user-desc">{{userInfo.phone || '暂未绑定手机'}}</text>
        </view>
      </view>
      <view class="customer-header-actions">
        <view class="customer-settings-icon" bindtap="goToPersonalInfo">⚙️</view>
      </view>
    </view>

    <!-- 快速服务 -->
    <view class="customer-service-menu">
      <view class="customer-service-title">快速服务</view>
      <view class="customer-service-grid">
        <view class="customer-service-item" bindtap="goToCreateOrder">
          <text class="customer-service-icon">🚚</text>
          <text class="customer-service-label">下单</text>
        </view>
        <view class="customer-service-item" bindtap="goToOrderList">
          <text class="customer-service-icon">📦</text>
          <text class="customer-service-label">订单</text>
        </view>
        <view class="customer-service-item" bindtap="goToAddressManage">
          <text class="customer-service-icon">📍</text>
          <text class="customer-service-label">地址簿</text>
        </view>
        <view class="customer-service-item" bindtap="goToIntelligentService">
          <text class="customer-service-icon">🤖</text>
          <text class="customer-service-label">客服</text>
        </view>
      </view>
    </view>

    <!-- 订单统计 -->
    <view class="customer-order-stats">
      <view class="customer-stat-item" bindtap="goToOrderList" data-type="pending">
        <text class="customer-stat-number">{{orderStats.pending || 0}}</text>
        <text class="customer-stat-label">待服务</text>
      </view>
      <view class="customer-stat-item" bindtap="goToOrderList" data-type="processing">
        <text class="customer-stat-number">{{orderStats.processing || 0}}</text>
        <text class="customer-stat-label">进行中</text>
      </view>
      <view class="customer-stat-item" bindtap="goToOrderList" data-type="completed">
        <text class="customer-stat-number">{{orderStats.completed || 0}}</text>
        <text class="customer-stat-label">已完成</text>
      </view>
    </view>

    <!-- 常用功能 -->
    <view class="customer-common-menu">
      <view class="customer-common-title">常用功能</view>
      <view class="customer-common-list">
        <view class="customer-common-item" bindtap="goToWallet">
          <text class="customer-common-icon">💰</text>
          <text class="customer-common-label">我的钱包</text>
          <text class="customer-common-desc">余额：¥{{userInfo.balance || '0.00'}}</text>
        </view>
        <view class="customer-common-item" bindtap="goToCoupon">
          <text class="customer-common-icon">🎟️</text>
          <text class="customer-common-label">优惠券</text>
          <text class="customer-common-desc">{{couponCount || 0}}张可用</text>
        </view>
        <view class="customer-common-item" bindtap="goToInvite">
          <text class="customer-common-icon">🤝</text>
          <text class="customer-common-label">邀请好友</text>
          <text class="customer-common-desc">推荐得奖励</text>
        </view>
        <view class="customer-common-item" bindtap="logout">
          <text class="customer-common-icon">🚪</text>
          <text class="customer-common-label">退出登录</text>
          <text class="customer-common-desc">安全退出</text>
        </view>
      </view>
    </view>
  </view>
</view>
