<view class="coupons-container">
    <view class="coupons-tabs">
        <view 
            class="coupons-tab {{activeTab === 0 ? 'active' : ''}}" 
            bindtap="switchTab" 
            data-tab="0"
        >
            可使用
        </view>
        <view 
            class="coupons-tab {{activeTab === 1 ? 'active' : ''}}" 
            bindtap="switchTab" 
            data-tab="1"
        >
            已使用
        </view>
        <view 
            class="coupons-tab {{activeTab === 2 ? 'active' : ''}}" 
            bindtap="switchTab" 
            data-tab="2"
        >
            已过期
        </view>
    </view>

    <scroll-view scroll-y class="coupons-list">
        <block wx:for="{{coupons}}" wx:key="index">
            <view class="coupon-item {{item.status}}">
                <view class="coupon-left">
                    <text class="coupon-icon">🎫</text>
                    <text class="coupon-amount">¥{{item.amount}}</text>
                    <text class="coupon-condition">满{{item.condition}}可用</text>
                </view>
                <view class="coupon-right">
                    <text class="coupon-title">{{item.title}}</text>
                    <text class="coupon-time">{{item.startTime}} - {{item.endTime}}</text>
                </view>
            </view>
        </block>
    </scroll-view>
</view>
