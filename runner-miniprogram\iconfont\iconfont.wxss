/* 通用图标字体样式 */
@font-face {
    font-family: 'iconfont';
    src: url('iconfont.ttf') format('truetype');
}

.iconfont {
    font-family: 'iconfont';
    font-size: 16px;
    font-style: normal;
    display: inline-block;
}

/* 基础图标 */
.icon-home::before { content: '\e601'; }
.icon-user::before { content: '\e602'; }
.icon-order::before { content: '\e603'; }
.icon-service::before { content: '\e604'; }
.icon-message::before { content: '\e605'; }
.icon-setting::before { content: '\e606'; }
.icon-task::before { content: '\e607'; }
.icon-wallet::before { content: '\e608'; }
.icon-help::before { content: '\e609'; }

/* 功能图标 */
.icon-add::before { content: '\e610'; }
.icon-edit::before { content: '\e611'; }
.icon-delete::before { content: '\e612'; }
.icon-search::before { content: '\e613'; }
.icon-share::before { content: '\e614'; }
.icon-download::before { content: '\e615'; }
.icon-upload::before { content: '\e616'; }

/* 状态图标 */
.icon-success::before { content: '\e617'; }
.icon-warning::before { content: '\e618'; }
.icon-error::before { content: '\e619'; }
.icon-info::before { content: '\e620'; }

/* 颜色定义 */
.icon-primary { color: #3498db; }
.icon-success-color { color: #2ecc71; }
.icon-warning-color { color: #f39c12; }
.icon-danger-color { color: #e74c3c; }
.icon-info-color { color: #34495e; }

/* 尺寸定义 */
.icon-small { font-size: 12px; }
.icon-medium { font-size: 16px; }
.icon-large { font-size: 24px; }
.icon-xlarge { font-size: 32px; }
