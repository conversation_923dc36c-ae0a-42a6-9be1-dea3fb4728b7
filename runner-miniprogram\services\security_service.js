// security_service.js
const SECURITY_API_BASE_URL = 'https://your-api-domain.com/security/';

const SecurityService = {
  /**
   * 生成安全令牌
   * @param {string} userId 用户ID
   * @param {object} context 上下文信息
   * @returns {Promise<string>} 安全令牌
   */
  generateSecurityToken(userId, context) {
    return new Promise((resolve, reject) => {
      // 优先使用本地缓存的令牌
      const cachedToken = wx.getStorageSync('securityToken');
      if (cachedToken) {
        resolve(cachedToken);
        return;
      }

      wx.request({
        url: `${SECURITY_API_BASE_URL}token/generate/`,
        method: 'POST',
        data: { userId, context },
        success: (res) => {
          const token = res.data.token || this.createLocalToken(userId);
          wx.setStorageSync('securityToken', token);
          resolve(token);
        },
        fail: (error) => {
          console.error('安全令牌生成失败:', error);
          const fallbackToken = this.createLocalToken(userId);
          resolve(fallbackToken);
        }
      });
    });
  },

  /**
   * 创建本地安全令牌
   * @param {string} userId 用户ID
   * @returns {string} 本地生成的安全令牌
   */
  createLocalToken(userId) {
    const timestamp = Date.now();
    const randomPart = Math.random().toString(36).substr(2, 9);
    return `ST-${userId}-${timestamp}-${randomPart}`;
  },

  /**
   * 检测异常行为
   * @param {object} userBehavior 用户行为数据
   * @returns {Promise<object>} 异常检测结果
   */
  detectAnomalousBehavior(userBehavior) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${SECURITY_API_BASE_URL}anomaly-detection/`,
        method: 'POST',
        data: userBehavior,
        success: (res) => {
          const result = res.data || this.generateLocalAnomalyResult(userBehavior);
          resolve(result);
        },
        fail: (error) => {
          console.error('异常行为检测失败:', error);
          const fallbackResult = this.generateLocalAnomalyResult(userBehavior);
          resolve(fallbackResult);
        }
      });
    });
  },

  /**
   * 生成本地异常结果
   * @param {object} userBehavior 用户行为数据
   * @returns {object} 本地生成的异常结果
   */
  generateLocalAnomalyResult(userBehavior) {
    const anomalyFactors = Object.values(userBehavior);
    const anomalyScore = anomalyFactors.reduce((sum, val) => sum + Math.abs(val - 1), 0) / anomalyFactors.length;

    return {
      anomalyLevel: this.determineRiskLevel(anomalyScore),
      anomalyScore: anomalyScore,
      details: userBehavior
    };
  },

  /**
   * 确定风险等级
   * @param {number} anomalyScore 异常分数
   * @returns {string} 风险等级
   */
  determineRiskLevel(anomalyScore) {
    if (anomalyScore < 0.3) return 'low';
    if (anomalyScore < 0.7) return 'medium';
    return 'high';
  },

  /**
   * 验证安全令牌
   * @param {string} token 安全令牌
   * @param {string} userId 用户ID
   * @param {object} context 上下文信息
   * @returns {Promise<boolean>} 令牌是否有效
   */
  validateSecurityToken(token, userId, context) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${SECURITY_API_BASE_URL}token/validate/`,
        method: 'POST',
        data: { token, userId, context },
        success: (res) => {
          const isValid = res.data.isValid || false;
          resolve(isValid);
        },
        fail: (error) => {
          console.error('安全令牌验证失败:', error);
          resolve(false);
        }
      });
    });
  },

  /**
   * 模拟测试方法：生成测试异常行为数据
   * @returns {object[]} 模拟的用户行为数据
   */
  generateTestBehaviorData() {
    const testScenarios = [
      {
        name: '正常行为',
        data: {
          interactionFrequency: 5,
          timeBetweenInteractions: 30,
          locationDiversity: 2,
          deviceChangeFrequency: 1,
          transactionAmountVariation: 0.2,
          navigationPatternDeviation: 0.1
        }
      },
      {
        name: '中等风险行为',
        data: {
          interactionFrequency: 15,
          timeBetweenInteractions: 10,
          locationDiversity: 5,
          deviceChangeFrequency: 3,
          transactionAmountVariation: 0.6,
          navigationPatternDeviation: 0.5
        }
      },
      {
        name: '高风险行为',
        data: {
          interactionFrequency: 30,
          timeBetweenInteractions: 2,
          locationDiversity: 10,
          deviceChangeFrequency: 6,
          transactionAmountVariation: 1.2,
          navigationPatternDeviation: 0.9
        }
      }
    ];

    return testScenarios;
  }
};

module.exports = SecurityService;
