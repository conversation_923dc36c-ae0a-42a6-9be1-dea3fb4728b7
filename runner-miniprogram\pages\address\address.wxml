<view class="address-container">
    <view class="address-list">
        <block wx:for="{{addresses}}" wx:key="index">
            <view class="address-item" bindtap="selectAddress" data-index="{{index}}">
                <view class="address-icon">📍</view>
                <view class="address-details">
                    <view class="address-header">
                        <text class="address-name">{{item.name}}</text>
                        <text class="address-phone">{{item.phone}}</text>
                        <text 
                            wx:if="{{item.isDefault}}" 
                            class="address-default-tag"
                        >
                            默认
                        </text>
                    </view>
                    <text class="address-text">{{item.detail}}</text>
                </view>
                <view class="address-actions">
                    <view 
                        class="address-edit" 
                        bindtap="editAddress" 
                        data-index="{{index}}"
                        catch:tap="true"
                    >
                        ✏️
                    </view>
                    <view 
                        class="address-delete" 
                        bindtap="deleteAddress" 
                        data-index="{{index}}"
                        catch:tap="true"
                    >
                        🗑️
                    </view>
                </view>
            </view>
        </block>
    </view>

    <view 
        class="address-add-btn" 
        bindtap="addNewAddress"
    >
        ➕ 添加新地址
    </view>
</view>
