<view class="container merchant-page">
    <view class="header">
        <image class="logo" src="{{merchantInfo.logoUrl}}"></image>
        <text class="merchant-name">{{merchantInfo.name}}</text>
    </view>

    <view class="order-stats">
        <view class="stat-item">
            <text class="stat-label">总订单</text>
            <text class="stat-value">{{orderStats.totalOrders}}</text>
        </view>
        <view class="stat-item">
            <text class="stat-label">已完成</text>
            <text class="stat-value">{{orderStats.completedOrders}}</text>
        </view>
        <view class="stat-item">
            <text class="stat-label">待处理</text>
            <text class="stat-value">{{orderStats.pendingOrders}}</text>
        </view>
    </view>

    <view class="orders-section">
        <text class="section-title">当前订单</text>
        <block wx:for="{{currentOrders}}" wx:key="id">
            <view class="order-card" bindtap="navigateToOrderDetail" data-order-id="{{item.id}}">
                <view class="order-info">
                    <text class="order-id">订单号: {{item.id}}</text>
                    <text class="order-type">{{item.type}}</text>
                    <text class="order-status">{{item.status}}</text>
                </view>
                <view class="order-actions">
                    <button 
                        class="process-btn confirm" 
                        data-order-id="{{item.id}}" 
                        data-action="confirm"
                        bindtap="processOrder"
                    >
                        确认
                    </button>
                    <button 
                        class="process-btn reject" 
                        data-order-id="{{item.id}}" 
                        data-action="reject"
                        bindtap="processOrder"
                    >
                        拒绝
                    </button>
                </view>
            </view>
        </block>
    </view>
</view>
