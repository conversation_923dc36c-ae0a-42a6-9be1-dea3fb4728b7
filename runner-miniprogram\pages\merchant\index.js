// 商家首页逻辑
Page({
    data: {
        merchantInfo: null,
        currentOrders: [],
        orderStats: {
            totalOrders: 0,
            completedOrders: 0,
            pendingOrders: 0
        }
    },

    onLoad() {
        this.initMerchantPage();
    },

    initMerchantPage() {
        this.checkMerchantLogin();
        this.fetchMerchantProfile();
        this.fetchCurrentOrders();
    },

    checkMerchantLogin() {
        wx.login({
            success: (res) => {
                if (res.code) {
                    this.performMerchantLogin(res.code);
                }
            }
        });
    },

    performMerchantLogin(code) {
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/merchant/login`,
            method: 'POST',
            data: { code: code },
            success: (loginRes) => {
                wx.setStorageSync('merchant_token', loginRes.data.token);
            }
        });
    },

    fetchMerchantProfile() {
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/merchant/profile`,
            method: 'GET',
            header: { 'Authorization': wx.getStorageSync('merchant_token') },
            success: (profileRes) => {
                this.setData({
                    merchantInfo: profileRes.data.profile
                });
            }
        });
    },

    fetchCurrentOrders() {
        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/merchant/orders`,
            method: 'GET',
            header: { 'Authorization': wx.getStorageSync('merchant_token') },
            success: (ordersRes) => {
                this.setData({
                    currentOrders: ordersRes.data.orders,
                    orderStats: ordersRes.data.stats
                });
            }
        });
    },

    processOrder(e) {
        const orderId = e.currentTarget.dataset.orderId;
        const action = e.currentTarget.dataset.action;

        wx.request({
            url: `${getApp().globalData.API_BASE_URL}/merchant/process_order`,
            method: 'POST',
            data: { 
                order_id: orderId,
                action: action
            },
            header: { 'Authorization': wx.getStorageSync('merchant_token') },
            success: () => {
                wx.showToast({ title: '订单处理成功' });
                this.fetchCurrentOrders();
            }
        });
    },

    navigateToOrderDetail(e) {
        const orderId = e.currentTarget.dataset.orderId;
        wx.navigateTo({
            url: `/pages/merchant/order_detail?id=${orderId}`
        });
    }
});
