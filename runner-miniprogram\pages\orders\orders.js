Page({
    data: {
        activeTab: 0,
        orders: [
            {
                id: '20231214001',
                title: '跑腿代取快递',
                description: '中通快递，顺义区',
                status: 'pending',
                statusText: '进行中',
                price: 5.00,
                time: '12月14日 14:30',
                imageEmoji: '📦'
            },
            {
                id: '20231214002',
                title: '帮我买早餐',
                description: '豆浆、油条、煎蛋',
                status: 'completed',
                statusText: '已完成',
                price: 12.50,
                time: '12月14日 07:45',
                imageEmoji: '🥐'
            },
            {
                id: '20231214003',
                title: '帮我排队',
                description: '医院挂号',
                status: 'cancelled',
                statusText: '已取消',
                price: 20.00,
                time: '12月13日 18:20',
                imageEmoji: '🏥'
            }
        ]
    },

    onLoad: function() {
        // 可以在这里加载真实的订单数据
    },

    switchTab: function(e) {
        const tab = e.currentTarget.dataset.tab;
        this.setData({
            activeTab: parseInt(tab)
        });
    },

    viewOrderDetail: function(e) {
        const orderId = e.currentTarget.dataset.id;
        wx.navigateTo({
            url: `/pages/order-detail/order-detail?id=${orderId}`
        });
    }
});
